"use strict";

import dotenv from 'dotenv';
import { supabase } from './supabase.js';

dotenv.config();

const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const YOUTUBE_BASE_URL = 'https://www.googleapis.com/youtube/v3';

export async function getVideos(channelId, maxResults = 20) {
  if (!YOUTUBE_API_KEY) {
    throw new Error('YouTube API key not configured');
  }

  try {
    const response = await fetch(
      `${YOUTUBE_BASE_URL}/search?` +
      new URLSearchParams({
        part: 'snippet',
        channelId: channelId,
        maxResults: maxResults.toString(),
        order: 'date',
        type: 'video',
        key: YOUTUBE_API_KEY
      })
    );

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }

    const data = await response.json();
    return data.items.map(item => ({
      title: item.snippet.title,
      description: item.snippet.description,
      url: `https://www.youtube.com/watch?v=${item.id.videoId}`,
      thumbnail_url: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      channel_name: item.snippet.channelTitle,
      channel_id: item.snippet.channelId,
      published_at: item.snippet.publishedAt
    }));
  } catch (error) {
    console.error('Error fetching videos from YouTube API:', error);
    throw error;
  }
}

export async function syncVideos() {
  try {
    // Get tracked channels
    const { data: channels, error: channelsError } = await supabase
      .from('channels')
      .select('*')
      .eq('is_tracked', true);

    if (channelsError) throw channelsError;

    const allVideos = [];
    
    for (const channel of channels || []) {
      try {
        const videos = await getVideos(channel.channel_id);
        
        // Insert videos into database
        for (const video of videos) {
          const { error: insertError } = await supabase
            .from('videos')
            .upsert(video, { onConflict: 'url' });
          
          if (!insertError) {
            allVideos.push(video);
          }
        }
      } catch (error) {
        console.error(`Error syncing videos for channel ${channel.name}:`, error);
      }
    }

    return { videos: allVideos, count: allVideos.length };
  } catch (error) {
    console.error('Error in syncVideos:', error);
    throw error;
  }
}