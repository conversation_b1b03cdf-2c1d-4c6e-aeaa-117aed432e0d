"use strict";

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function initDatabase() {
  console.log('🔄 Initializing Velio App Database...\n');

  // Validate environment variables
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
    console.error('❌ Missing Supabase configuration!');
    console.error('Please set SUPABASE_URL and SUPABASE_ANON_KEY in your .env file');
    process.exit(1);
  }

  // Create Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );

  try {
    // Test connection
    console.log('🔍 Testing Supabase connection...');
    const { data, error } = await supabase.from('_test').select('*').limit(1);
    
    if (error && !error.message.includes('does not exist')) {
      throw new Error(`Connection failed: ${error.message}`);
    }
    
    console.log('✅ Supabase connection successful\n');

    // Create tables
    console.log('📋 Creating database tables...');
    
    // Channels table
    await createTable('channels', `
      CREATE TABLE IF NOT EXISTS channels (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        channel_id TEXT NOT NULL UNIQUE,
        url TEXT NOT NULL,
        description TEXT,
        subscriber_count INTEGER DEFAULT 0,
        is_tracked BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Videos table
    await createTable('videos', `
      CREATE TABLE IF NOT EXISTS videos (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        url TEXT NOT NULL UNIQUE,
        video_id TEXT,
        thumbnail_url TEXT,
        channel_name TEXT NOT NULL,
        channel_id TEXT NOT NULL,
        published_at TIMESTAMP WITH TIME ZONE,
        duration TEXT,
        view_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes
    console.log('🏗️  Creating database indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id)',
      'CREATE INDEX IF NOT EXISTS idx_videos_published_at ON videos(published_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_videos_url ON videos(url)',
      'CREATE INDEX IF NOT EXISTS idx_channels_tracked ON channels(is_tracked)',
      'CREATE INDEX IF NOT EXISTS idx_channels_channel_id ON channels(channel_id)'
    ];

    for (const indexSql of indexes) {
      const { error } = await supabase.rpc('execute_sql', { sql: indexSql });
      if (error && !error.message.includes('already exists')) {
        console.warn(`⚠️  Index warning: ${error.message}`);
      }
    }

    // Insert sample data
    console.log('📦 Inserting sample data...');
    
    const sampleChannels = [
      {
        name: 'Demo Tech Channel',
        channel_id: 'demo_tech_123',
        url: 'https://www.youtube.com/@demo-tech',
        description: 'Demo channel for testing - replace with real channels',
        is_tracked: true
      },
      {
        name: 'Demo Education Channel', 
        channel_id: 'demo_edu_456',
        url: 'https://www.youtube.com/@demo-education',
        description: 'Demo educational content - replace with real channels',
        is_tracked: false
      }
    ];

    const { error: insertError } = await supabase
      .from('channels')
      .upsert(sampleChannels, { onConflict: 'channel_id' });

    if (insertError) {
      console.warn(`⚠️  Sample data warning: ${insertError.message}`);
    } else {
      console.log('✅ Sample channels inserted');
    }

    // Verify setup
    console.log('\n🔍 Verifying database setup...');
    
    const { data: channels, error: channelsError } = await supabase
      .from('channels')
      .select('*');

    if (channelsError) {
      throw new Error(`Verification failed: ${channelsError.message}`);
    }

    console.log(`✅ Found ${channels.length} channels in database`);

    const { data: videos, error: videosError } = await supabase
      .from('videos')
      .select('*');

    if (videosError) {
      throw new Error(`Verification failed: ${videosError.message}`);
    }

    console.log(`✅ Found ${videos.length} videos in database`);

    console.log('\n🎉 Database initialization complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Get a YouTube Data API key from Google Cloud Console');
    console.log('2. Add it to your .env file as YOUTUBE_API_KEY');
    console.log('3. Start the development server: pnpm run dev');
    console.log('4. Visit http://localhost:3000 to use the app');

  } catch (error) {
    console.error('\n❌ Database initialization failed:');
    console.error(error.message);
    process.exit(1);
  }

  async function createTable(tableName, sql) {
    try {
      const { error } = await supabase.rpc('execute_sql', { sql });
      
      if (error) {
        // Try direct query if RPC fails
        const { error: directError } = await supabase.from(tableName).select('id').limit(1);
        if (directError && directError.message.includes('does not exist')) {
          throw new Error(`Failed to create ${tableName} table: ${error.message}`);
        }
      }
      
      console.log(`✅ Table '${tableName}' ready`);
    } catch (err) {
      console.error(`❌ Error with table '${tableName}':`, err.message);
      throw err;
    }
  }
}

// Create execute_sql function if it doesn't exist
async function ensureExecuteSqlFunction() {
  console.log('🔧 Ensuring execute_sql function exists...');
  
  // This needs to be run manually in Supabase SQL editor if it fails
  const functionSql = `
    CREATE OR REPLACE FUNCTION execute_sql(sql text)
    RETURNS text AS $$
    BEGIN
        EXECUTE sql;
        RETURN 'Success';
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'Error: ' || SQLERRM;
    END;
    $$ LANGUAGE plpgsql;
  `;

  console.log('📝 Please run this SQL in your Supabase SQL Editor:');
  console.log('=' .repeat(60));
  console.log(functionSql);
  console.log('=' .repeat(60));
}

if (import.meta.url === `file://${process.argv[1]}`) {
  initDatabase().catch(console.error);
}

export { initDatabase };
