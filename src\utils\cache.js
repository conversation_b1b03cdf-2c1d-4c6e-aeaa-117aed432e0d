"use strict";

/**
 * Simple in-memory cache for user profile data
 * Reduces database load for frequently accessed user profiles
 */

class ProfileCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5 minutes TTL
    this.maxSize = 1000; // Maximum number of cached profiles
  }

  /**
   * Generate cache key for user profile
   * @param {string} userId - User ID
   * @returns {string} Cache key
   */
  _getCacheKey(userId) {
    return `profile:${userId}`;
  }

  /**
   * Check if cache entry is expired
   * @param {Object} entry - Cache entry
   * @returns {boolean} True if expired
   */
  _isExpired(entry) {
    return Date.now() - entry.timestamp > this.ttl;
  }

  /**
   * Clean up expired entries
   */
  _cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Ensure cache doesn't exceed max size
   */
  _enforceMaxSize() {
    if (this.cache.size >= this.maxSize) {
      // Remove oldest entries (simple LRU)
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remove oldest 10% of entries
      const toRemove = Math.floor(this.maxSize * 0.1);
      for (let i = 0; i < toRemove; i++) {
        this.cache.delete(entries[i][0]);
      }
    }
  }

  /**
   * Get user profile from cache
   * @param {string} userId - User ID
   * @returns {Object|null} Cached profile or null if not found/expired
   */
  get(userId) {
    const key = this._getCacheKey(userId);
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    if (this._isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }
    
    // Update access time for LRU
    entry.lastAccessed = Date.now();
    return entry.data;
  }

  /**
   * Set user profile in cache
   * @param {string} userId - User ID
   * @param {Object} profile - Profile data
   */
  set(userId, profile) {
    const key = this._getCacheKey(userId);
    const now = Date.now();
    
    this._enforceMaxSize();
    
    this.cache.set(key, {
      data: profile,
      timestamp: now,
      lastAccessed: now
    });
  }

  /**
   * Remove user profile from cache
   * @param {string} userId - User ID
   */
  delete(userId) {
    const key = this._getCacheKey(userId);
    this.cache.delete(key);
  }

  /**
   * Clear all cached profiles
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache stats
   */
  getStats() {
    this._cleanup();
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    };
  }

  /**
   * Start periodic cleanup
   */
  startCleanup() {
    // Clean up expired entries every 2 minutes
    this.cleanupInterval = setInterval(() => {
      this._cleanup();
    }, 2 * 60 * 1000);
  }

  /**
   * Stop periodic cleanup
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// Create singleton instance
const profileCache = new ProfileCache();

// Start automatic cleanup
profileCache.startCleanup();

// Graceful shutdown
process.on('SIGTERM', () => {
  profileCache.stopCleanup();
});

process.on('SIGINT', () => {
  profileCache.stopCleanup();
});

export { profileCache };
