# VirSnapp Database Schema Documentation

## Overview
This document describes the Supabase database schema for the VirSnapp YouTube channel tracker application.

## Database Connection
- **Supabase Project**: Virsnapp (ID: hjtivhjctbmjkqtqnqcv)
- **Region**: eu-west-3
- **Environment Variables**:
  - `SUPABASE_URL`: Your Supabase project URL
  - `SUPABASE_ANON_KEY`: Anonymous/public key for client-side operations
  - `SUPABASE_SERVICE_KEY`: Service role key for server-side operations

## Tables

### 1. user_profiles
Stores user profile information and preferences.

**Columns:**
- `id` (UUID, Primary Key): Internal profile ID
- `user_id` (UUID, Unique): References Supabase auth.users.id
- `email` (VARCHAR, Unique): User's email address
- `full_name` (VARCHAR): User's display name
- `avatar_url` (TEXT): URL to user's profile picture
- `bio` (TEXT): User biography/description
- `preferences` (JSONB): User preferences and settings
- `settings` (JSONB): Application settings with defaults:
  ```json
  {
    "theme": "dark",
    "notifications": {
      "email": true,
      "push": true,
      "new_videos": true,
      "channel_updates": true
    },
    "privacy": {
      "profile_visibility": "private",
      "show_saved_videos": false
    },
    "youtube": {
      "auto_save_liked": false,
      "default_quality": "720p",
      "auto_play": false
    }
  }
  ```
- `subscription_tier` (VARCHAR): 'free', 'premium', etc.
- `subscription_status` (VARCHAR): 'active', 'cancelled', etc.
- `last_login` (TIMESTAMPTZ): Last login timestamp
- `created_at` (TIMESTAMPTZ): Profile creation timestamp
- `updated_at` (TIMESTAMPTZ): Last update timestamp

### 2. youtube_channels
Stores information about YouTube channels being tracked.

**Columns:**
- `id` (UUID, Primary Key): Internal channel ID
- `channel_id` (VARCHAR, Unique): YouTube channel ID
- `channel_name` (VARCHAR): Channel display name
- `channel_handle` (VARCHAR): Channel handle (@username)
- `description` (TEXT): Channel description
- `thumbnail_url` (TEXT): Channel thumbnail URL
- `subscriber_count` (BIGINT): Number of subscribers
- `video_count` (BIGINT): Number of videos
- `view_count` (BIGINT): Total view count
- `country` (VARCHAR): Channel's country
- `published_at` (TIMESTAMPTZ): Channel creation date
- `last_updated` (TIMESTAMPTZ): Last data update
- `created_at` (TIMESTAMPTZ): Record creation timestamp

### 3. user_channel_subscriptions
Many-to-many relationship between users and YouTube channels.

**Columns:**
- `id` (UUID, Primary Key): Subscription ID
- `user_id` (UUID, Foreign Key): References user_profiles.user_id
- `channel_id` (UUID, Foreign Key): References youtube_channels.id
- `notification_enabled` (BOOLEAN): Whether notifications are enabled
- `subscribed_at` (TIMESTAMPTZ): Subscription timestamp

### 4. saved_videos
Stores videos saved by users.

**Columns:**
- `id` (UUID, Primary Key): Saved video ID
- `user_id` (UUID, Foreign Key): References user_profiles.user_id
- `video_id` (VARCHAR): YouTube video ID
- `video_title` (VARCHAR): Video title
- `video_description` (TEXT): Video description
- `thumbnail_url` (TEXT): Video thumbnail URL
- `channel_id` (UUID, Foreign Key): References youtube_channels.id
- `duration` (VARCHAR): Video duration (e.g., "PT10M30S")
- `published_at` (TIMESTAMPTZ): Video publication date
- `view_count` (BIGINT): Video view count
- `like_count` (BIGINT): Video like count
- `tags` (TEXT[]): Video tags array
- `category` (VARCHAR): Video category
- `notes` (TEXT): User's personal notes
- `is_watched` (BOOLEAN): Whether user has watched the video
- `watch_progress` (INTEGER): Watch progress in seconds
- `saved_at` (TIMESTAMPTZ): When video was saved
- `watched_at` (TIMESTAMPTZ): When video was marked as watched

## Row Level Security (RLS)

All tables have RLS enabled with the following policies:

### user_profiles
- Users can view, update, and insert their own profile
- Uses `auth.uid() = user_id` for access control

### user_channel_subscriptions
- Users can manage their own subscriptions
- Uses `auth.uid() = user_id` for access control

### saved_videos
- Users can manage their own saved videos
- Uses `auth.uid() = user_id` for access control

### youtube_channels
- All authenticated users can view channel data (public information)
- Only service role can insert/update channel data

## API Endpoints

### Authentication Required Endpoints
- `GET /auth/profile` - View user profile page
- `POST /auth/profile` - Update user profile
- `GET /api/profile` - Get user profile data (JSON API)

## Usage Examples

### Creating a User Profile (Automatic on Signup)
```javascript
const { data, error } = await supabaseAdmin
  .from('user_profiles')
  .insert({
    user_id: user.id,
    email: user.email,
    full_name: 'User Name',
    preferences: {
      last_login: new Date().toISOString()
    }
  });
```

### Fetching User Profile
```javascript
const { data: profile, error } = await supabaseAdmin
  .from('user_profiles')
  .select('*')
  .eq('user_id', userId)
  .single();
```

### Saving a Video
```javascript
const { data, error } = await supabase
  .from('saved_videos')
  .insert({
    user_id: userId,
    video_id: 'dQw4w9WgXcQ',
    video_title: 'Never Gonna Give You Up',
    video_description: 'Rick Astley - Never Gonna Give You Up',
    thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    duration: 'PT3M33S',
    published_at: '2009-10-25T06:57:33Z'
  });
```

## Database Status
✅ **Connected**: Supabase project is active and healthy  
✅ **Schema Created**: All tables, indexes, and constraints are in place  
✅ **RLS Configured**: Row Level Security policies are active  
✅ **Integration Tested**: Authentication and profile management working  
✅ **Ready for Production**: Database is ready for application use  

## Next Steps
1. Implement YouTube API integration to populate channel and video data
2. Add video search and filtering functionality
3. Implement notification system for new videos
4. Add analytics and usage tracking
5. Implement subscription management features
