"use strict";

export function initSidebar() {
  const sidebar = document.getElementById('sidebar');
  const collapseButton = document.getElementById('sidebar-collapse-button');
  const sidebarTexts = document.querySelectorAll('.sidebar-text');
  
  if (!sidebar || !collapseButton) return;

  // Load saved state
  const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
  if (isCollapsed) {
    collapseSidebar();
  }

  collapseButton.addEventListener('click', () => {
    if (sidebar.classList.contains('w-16')) {
      expandSidebar();
    } else {
      collapseSidebar();
    }
  });

  function collapseSidebar() {
    sidebar.classList.remove('w-64');
    sidebar.classList.add('w-16');
    
    // Hide all text elements
    sidebarTexts.forEach(text => {
      text.classList.add('hidden');
    });
    
    // Update navigation items for collapsed state
    const navItems = sidebar.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      // Center the entire nav item
      item.classList.add('justify-center');
      item.classList.remove('px-3');
      item.classList.add('px-2');
      
      // Update icon container and icon size for collapsed state
      const iconContainer = item.querySelector('.nav-icon-container');
      const icon = item.querySelector('.nav-icon-container svg');
      
      if (iconContainer) {
        // Make icon container match logo size (w-8 h-8)
        iconContainer.classList.remove('w-5', 'h-5');
        iconContainer.classList.add('w-8', 'h-8');
      }
      
      if (icon) {
        // Make icon larger to match logo icon size
        icon.classList.remove('w-5', 'h-5');
        icon.classList.add('w-5', 'h-5'); // Keep icon itself at w-5 h-5 but in larger container
      }
      
      // Hide the text span
      const textSpan = item.querySelector('.sidebar-text');
      if (textSpan) {
        textSpan.classList.add('hidden');
        textSpan.classList.remove('ml-3');
      }
    });
    
    // Center the logo in the header
    const logoHeader = sidebar.querySelector('div:first-child');
    if (logoHeader) {
      logoHeader.classList.remove('justify-between');
      logoHeader.classList.add('justify-center');
    }
    
    // Rotate collapse button icon
    const buttonIcon = collapseButton.querySelector('svg');
    if (buttonIcon) {
      buttonIcon.style.transform = 'rotate(180deg)';
    }
    
    localStorage.setItem('sidebar-collapsed', 'true');
  }

  function expandSidebar() {
    sidebar.classList.remove('w-16');
    sidebar.classList.add('w-64');
    
    // Show all text elements
    sidebarTexts.forEach(text => {
      text.classList.remove('hidden');
    });
    
    // Reset navigation items for expanded state
    const navItems = sidebar.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      // Reset nav item alignment
      item.classList.remove('justify-center');
      item.classList.remove('px-2');
      item.classList.add('px-3');
      
      // Reset icon container and icon size for expanded state
      const iconContainer = item.querySelector('.nav-icon-container');
      const icon = item.querySelector('.nav-icon-container svg');
      
      if (iconContainer) {
        // Reset icon container to original size
        iconContainer.classList.remove('w-8', 'h-8');
        iconContainer.classList.add('w-5', 'h-5');
      }
      
      if (icon) {
        // Reset icon to original size
        icon.classList.remove('w-6', 'h-6');
        icon.classList.add('w-5', 'h-5');
      }
      
      // Show the text span and restore spacing
      const textSpan = item.querySelector('.sidebar-text');
      if (textSpan) {
        textSpan.classList.remove('hidden');
        textSpan.classList.add('ml-3');
      }
    });
    
    // Reset logo header layout
    const logoHeader = sidebar.querySelector('div:first-child');
    if (logoHeader) {
      logoHeader.classList.remove('justify-center');
      logoHeader.classList.add('justify-between');
    }
    
    // Reset collapse button icon
    const buttonIcon = collapseButton.querySelector('svg');
    if (buttonIcon) {
      buttonIcon.style.transform = 'rotate(0deg)';
    }
    
    localStorage.setItem('sidebar-collapsed', 'false');
  }
}