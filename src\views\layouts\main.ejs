<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - VirSnapp</title>
  <link href="/public/css/style.css" rel="stylesheet">
  <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  <script src="/public/js/app.min.js" defer></script>
</head>
<body class="bg-gray-900 text-gray-100">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <%- include('../partials/sidebar') %>
    
    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto transition-all duration-300 ease-in-out" id="main-content">
      <div class="p-6">
        <!-- Mobile Header -->
        <div class="flex items-center justify-between mb-6 lg:hidden">
          <button 
            id="mobile-menu-button" 
            class="p-2 rounded-lg bg-gray-800 text-gray-300 hover:bg-gray-700 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
          <h1 class="text-xl font-bold text-white"><%= title %></h1>
          <div class="w-10"></div> <!-- Spacer for balance -->
        </div>

        <!-- Page Content -->
        <div hx-boost="true" hx-target="#main-content" hx-swap="innerHTML">
          <%- body %>
        </div>
      </div>
    </main>
  </div>

  <!-- Mobile Sidebar Overlay -->
  <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>
</body>
</html>