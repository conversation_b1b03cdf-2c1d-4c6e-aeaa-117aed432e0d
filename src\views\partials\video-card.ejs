<div class="bg-gray-800 rounded-lg shadow-sm border border-gray-700 overflow-hidden hover:shadow-md transition-shadow">
  <div class="relative">
    <img 
      src="<%= video.thumbnail_url %>" 
      alt="<%= video.title %>" 
      class="w-full h-48 object-cover"
    >
    <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
      <%= video.duration || '0:00' %>
    </div>
    <div class="absolute top-2 right-2">
      <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
      </svg>
    </div>
  </div>
  <div class="p-4">
    <h3 class="font-semibold text-white mb-2 line-clamp-2 leading-tight">
      <%= video.title %>
    </h3>
    <p class="text-sm text-gray-400 mb-2">
      <%= video.channel_name %>
    </p>
    <div class="flex items-center justify-between text-xs text-gray-500">
      <span><%= video.view_count ? video.view_count.toLocaleString() : '0' %> views</span>
      <span><%= new Date(video.published_at).toLocaleDateString() %></span>
    </div>
    <div class="flex items-center justify-between mt-3">
      <a 
        href="<%= video.url %>" 
        target="_blank" 
        class="inline-flex items-center px-3 py-1 text-xs font-medium text-green-300 bg-green-900/20 rounded-full hover:bg-green-900/40 transition-colors"
      >
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Watch
      </a>
      <button class="p-1 text-gray-500 hover:text-gray-300 transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
        </svg>
      </button>
    </div>
  </div>
</div>