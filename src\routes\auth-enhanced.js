"use strict";

import { supabase, supabaseAdmin } from '../utils/supabase.js';
import { validateInput, schemas } from '../utils/validation.js';
import { uploadFile, deleteFile } from '../utils/storage.js';

async function authRoutes(fastify, options) {
  
  // Enhanced authentication middleware
  fastify.decorate('authenticate', async function (request, reply) {
    try {
      const token = request.cookies.token;
      if (!token) {
        throw new Error('No authentication token');
      }
      
      const decoded = fastify.jwt.verify(token);
      
      // Verify user still exists in Supabase
      const { data: user, error } = await supabase.auth.getUser(decoded.supabaseToken);
      if (error || !user) {
        throw new Error('Invalid user session');
      }
      
      request.user = {
        id: decoded.userId,
        email: decoded.email,
        supabaseUser: user
      };
    } catch (err) {
      reply.code(401).send({ 
        error: 'Authentication required',
        redirect: '/auth/signin' 
      });
    }
  });

  // Optional authentication (doesn't fail if not authenticated)
  fastify.decorate('optionalAuth', async function (request, reply) {
    try {
      const token = request.cookies.token;
      if (token) {
        const decoded = fastify.jwt.verify(token);
        request.user = {
          id: decoded.userId,
          email: decoded.email
        };
      }
    } catch (err) {
      // Silently fail for optional auth
      request.user = null;
    }
  });

  // Sign-in page route
  fastify.get('/auth/signin', async (request, reply) => {
    // Don't redirect if already authenticated - let the user see the signin page
    // The middleware will handle authentication checks for protected routes

    // Read the signin template directly and send as HTML
    const fs = await import('fs/promises');
    const path = await import('path');
    const { fileURLToPath } = await import('url');

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const templatePath = path.join(__dirname, '../views/auth/signin.ejs');

    try {
      const template = await fs.readFile(templatePath, 'utf8');
      const html = template.replace('<%= title %>', 'Sign In');

      return reply
        .type('text/html')
        .send(html);
    } catch (error) {
      fastify.log.error('Error reading signin template:', error);
      return reply.code(500).send('Internal Server Error');
    }
  });

  // Sign-up page route
  fastify.get('/auth/signup', async (request, reply) => {
    // Read the signup template directly and send as HTML
    const fs = await import('fs/promises');
    const path = await import('path');
    const { fileURLToPath } = await import('url');

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const templatePath = path.join(__dirname, '../views/auth/signup.ejs');

    try {
      const template = await fs.readFile(templatePath, 'utf8');
      const html = template.replace('<%= title %>', 'Sign Up');

      return reply
        .type('text/html')
        .send(html);
    } catch (error) {
      fastify.log.error('Error reading signup template:', error);
      return reply.code(500).send('Internal Server Error');
    }
  });

  // Login route
  fastify.post('/auth/login', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 6 },
          remember: { type: 'boolean' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { email, password, remember } = request.body;

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        return reply.code(401).send({ 
          error: 'Invalid credentials',
          message: error.message 
        });
      }

      // Get user profile using admin client
      const { data: profile } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('user_id', data.user.id)
        .single();

      // Create profile if it doesn't exist (for existing users)
      if (!profile) {
        await supabaseAdmin
          .from('user_profiles')
          .insert({
            user_id: data.user.id,
            email: data.user.email,
            full_name: data.user.user_metadata?.full_name || '',
            preferences: {
              last_login: new Date().toISOString()
            }
          });
      }

      const maxAge = remember ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000; // 30 days or 24 hours

      const token = fastify.jwt.sign({
        userId: data.user.id,
        email: data.user.email,
        supabaseToken: data.session.access_token
      });

      reply.setCookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge
      });

      // Update last login using admin client
      await supabaseAdmin
        .from('user_profiles')
        .update({
          last_login: new Date().toISOString(),
          preferences: {
            ...profile?.preferences,
            last_login: new Date().toISOString()
          }
        })
        .eq('user_id', data.user.id);

      // Return success response for client-side redirect
      return {
        success: true,
        user: data.user,
        profile: profile,
        redirect: '/'
      };
    } catch (error) {
      fastify.log.error(error);
      reply.code(500);
      return { error: 'Authentication failed' };
    }
  });

  // Sign-up route
  fastify.post('/auth/signup', {
    schema: {
      body: {
        type: 'object',
        required: ['email', 'password', 'fullName'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          fullName: { type: 'string', minLength: 2, maxLength: 100 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { email, password, fullName } = request.body;

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      });

      if (error) {
        return reply.code(400).send({
          error: 'Registration failed',
          message: error.message
        });
      }

      // Create user profile if user was created successfully
      if (data.user) {
        try {
          const { error: profileError } = await supabaseAdmin
            .from('user_profiles')
            .insert({
              user_id: data.user.id,
              email: data.user.email,
              full_name: fullName,
              preferences: {
                last_login: new Date().toISOString()
              }
            });

          if (profileError) {
            fastify.log.warn('Failed to create user profile:', profileError);
          }
        } catch (profileError) {
          // Log error but don't fail registration
          fastify.log.warn('Failed to create user profile:', profileError);
        }
      }

      return {
        success: true,
        message: 'Registration successful. Please check your email to verify your account.',
        requiresVerification: !data.session
      };
    } catch (error) {
      fastify.log.error(error);
      reply.code(500);
      return { error: 'Registration failed' };
    }
  });

  // Logout route
  fastify.post('/auth/logout', async (request, reply) => {
    try {
      // Sign out from Supabase
      await supabase.auth.signOut();
      
      reply.clearCookie('token');
      
      // For HTMX requests, return redirect instruction
      if (request.headers['hx-request']) {
        reply.header('HX-Redirect', '/auth/signin');
        return { success: true };
      }
      
      return reply.redirect('/auth/signin');
    } catch (error) {
      fastify.log.error(error);
      return { error: 'Logout failed' };
    }
  });

  // Get current user profile
  fastify.get('/auth/profile', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { data: profile, error } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('user_id', request.user.id)
        .single();

      if (error) throw error;

      return { 
        user: request.user,
        profile: profile || {}
      };
    } catch (error) {
      fastify.log.error(error);
      reply.code(500);
      return { error: 'Failed to fetch profile' };
    }
  });

  // Update user profile
  fastify.put('/auth/profile', {
    preHandler: [fastify.authenticate],
    schema: {
      body: {
        type: 'object',
        properties: {
          fullName: { type: 'string', minLength: 2, maxLength: 100 },
          bio: { type: 'string', maxLength: 500 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { fullName, bio } = request.body;
      
      const updates = {};
      if (fullName !== undefined) updates.full_name = fullName;
      if (bio !== undefined) updates.bio = bio;
      
      const { data, error } = await supabaseAdmin
        .from('user_profiles')
        .update(updates)
        .eq('user_id', request.user.id)
        .select()
        .single();

      if (error) throw error;

      return { 
        success: true, 
        profile: data,
        message: 'Profile updated successfully'
      };
    } catch (error) {
      fastify.log.error(error);
      reply.code(500);
      return { error: 'Failed to update profile' };
    }
  });

  // Upload profile avatar
  fastify.post('/auth/avatar', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const data = await request.file({
        limits: {
          fileSize: 5 * 1024 * 1024 // 5MB limit
        }
      });

      if (!data) {
        return reply.code(400).send({ error: 'No file uploaded' });
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(data.mimetype)) {
        return reply.code(400).send({ 
          error: 'Invalid file type. Only JPG, PNG, and WebP are allowed.' 
        });
      }

      // Delete old avatar if exists
      const { data: currentProfile } = await supabase
        .from('user_profiles')
        .select('avatar_url')
        .eq('user_id', request.user.id)
        .single();

      if (currentProfile?.avatar_url) {
        try {
          await deleteFile('avatars', currentProfile.avatar_url);
        } catch (err) {
          // Log but don't fail if old file deletion fails
          fastify.log.warn('Failed to delete old avatar:', err);
        }
      }

      // Upload new avatar
      const fileBuffer = await data.toBuffer();
      const fileName = `${request.user.id}/avatar-${Date.now()}.${data.mimetype.split('/')[1]}`;
      
      const avatarUrl = await uploadFile('avatars', fileName, fileBuffer, data.mimetype);

      // Update profile with new avatar URL
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ avatar_url: avatarUrl })
        .eq('user_id', request.user.id);

      if (updateError) throw updateError;

      return { 
        success: true, 
        avatarUrl,
        message: 'Avatar updated successfully'
      };
    } catch (error) {
      fastify.log.error(error);
      
      if (error.code === 'LIMIT_FILE_SIZE') {
        return reply.code(400).send({ error: 'File too large. Maximum size is 5MB.' });
      }
      
      reply.code(500);
      return { error: 'Failed to upload avatar' };
    }
  });

  // Delete profile avatar
  fastify.delete('/auth/avatar', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('avatar_url')
        .eq('user_id', request.user.id)
        .single();

      if (profile?.avatar_url) {
        await deleteFile('avatars', profile.avatar_url);
      }

      const { error } = await supabase
        .from('user_profiles')
        .update({ avatar_url: null })
        .eq('user_id', request.user.id);

      if (error) throw error;

      return { 
        success: true,
        message: 'Avatar removed successfully'
      };
    } catch (error) {
      fastify.log.error(error);
      reply.code(500);
      return { error: 'Failed to remove avatar' };
    }
  });

  // Check authentication status
  fastify.get('/auth/status', {
    preHandler: [fastify.optionalAuth]
  }, async (request, reply) => {
    return { 
      authenticated: !!request.user,
      user: request.user || null
    };
  });

  // API endpoint to get user profile data (for testing database integration)
  fastify.get('/api/profile', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { data: profile, error } = await supabaseAdmin
        .from('user_profiles')
        .select('*')
        .eq('user_id', request.user.id)
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        profile: {
          ...profile,
          // Don't expose internal ID
          id: undefined
        }
      };
    } catch (error) {
      fastify.log.error('API Profile fetch error:', error);
      reply.code(500);
      return { error: 'Failed to load profile data' };
    }
  });
}

export default authRoutes;
