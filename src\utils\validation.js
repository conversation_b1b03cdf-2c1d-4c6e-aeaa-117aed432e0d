"use strict";

import <PERSON><PERSON> from 'joi';

export function validateEnvironment() {
  const schema = Joi.object({
    NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
    FASTIFY_PORT: Joi.number().port().default(3000),
    JWT_SECRET: Joi.string().min(32).required().messages({
      'string.min': 'JWT_SECRET must be at least 32 characters long',
      'any.required': 'JWT_SECRET is required. Generate a secure secret key.'
    }),
    SUPABASE_URL: Joi.string().uri().required().messages({
      'any.required': 'SUPABASE_URL is required. Get this from your Supabase project settings.'
    }),
    SUPABASE_ANON_KEY: Joi.string().required().messages({
      'any.required': 'SUPABASE_ANON_KEY is required. Get this from your Supabase project settings.'
    }),
    YOUTUBE_API_KEY: Joi.string().when('NODE_ENV', {
      is: 'production',
      then: Joi.required().messages({
        'any.required': 'YOUTUBE_API_KEY is required in production. Get this from Google Cloud Console.'
      }),
      otherwise: Joi.optional()
    }),
    CORS_ORIGINS: Joi.string().optional(),
    COOKIE_SECRET: Joi.string().optional()
  });

  const { error, value } = schema.validate(process.env, {
    allowUnknown: true,
    stripUnknown: false
  });

  if (error) {
    console.error('❌ Environment validation failed:');
    console.error(error.details.map(d => `  - ${d.message}`).join('\n'));
    
    if (process.env.NODE_ENV === 'production') {
      console.error('\n🔧 To fix these issues:');
      console.error('1. Copy .env.example to .env');
      console.error('2. Fill in the required values');
      console.error('3. Restart the application');
      process.exit(1);
    } else {
      console.warn('⚠️  Continuing in development mode with validation errors');
      console.warn('📝 Please configure your .env file for full functionality');
    }
  } else {
    console.log('✅ Environment validation passed');
  }

  return value;
}

// Input validation schemas
export const schemas = {
  channelUrl: Joi.string().uri().pattern(/youtube\.com/).required().messages({
    'string.pattern.base': 'Must be a valid YouTube channel URL'
  }),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required().messages({
    'string.min': 'Password must be at least 8 characters long'
  }),
  channelId: Joi.string().required(),
  videoId: Joi.string().required(),
  pagination: {
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }
};

export function validateInput(schema, data) {
  const { error, value } = schema.validate(data);
  if (error) {
    const validationError = new Error('Validation failed');
    validationError.statusCode = 400;
    validationError.validation = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    throw validationError;
  }
  return value;
}

export function createValidationPreHandler(schema) {
  return async function(request, reply) {
    try {
      const validated = validateInput(schema, request.body || request.query);
      request.validated = validated;
    } catch (error) {
      reply.status(400).send({
        error: 'Validation Error',
        details: error.validation
      });
    }
  };
}
