"use strict";

import { getVideos, syncVideos } from '../utils/youtube-api.js';
import { supabase } from '../utils/supabase.js';
import { requireAuth } from '../middleware/auth.js';

async function videoRoutes(fastify, options) {
  // Get videos with HTMX response - requires authentication
  fastify.get('/api/videos', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    try {
      // Check if Supabase is properly configured
      if (process.env.SUPABASE_URL === 'https://your-project.supabase.co' ||
          process.env.SUPABASE_ANON_KEY === 'your_supabase_anon_key_here') {
        // Return demo/placeholder data when Supabase is not configured
        const demoVideos = [
          {
            id: '1',
            title: 'Demo Video - Configure Supabase to see real data',
            description: 'This is a demo video. Configure your Supabase credentials in .env to see real videos.',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            thumbnail_url: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
            channel_name: 'Demo Channel',
            channel_id: 'demo-channel',
            published_at: new Date().toISOString(),
            duration: '3:32',
            view_count: 1000000,
            created_at: new Date().toISOString()
          }
        ];

        return reply.view('partials/video-list', {
          videos: demoVideos,
          layout: false,
          title: 'Demo Videos'
        });
      }

      const { data: videos, error } = await supabase
        .from('videos')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      return reply.view('partials/video-list', {
        videos: videos || [],
        layout: false,
        title: 'Videos'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send(`
        <div class="p-4 bg-red-900/20 border border-red-800 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
            </svg>
            <span class="text-red-200">Error loading videos. Please check your Supabase configuration.</span>
          </div>
        </div>
      `);
    }
  });

  // Trigger video sync/scraping - requires authentication
  fastify.post('/api/sync-scrape', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    try {
      // Show loading state
      reply.header('HX-Trigger', 'videoSyncStarted');

      // Check if Supabase is properly configured
      if (process.env.SUPABASE_URL === 'https://your-project.supabase.co' ||
          process.env.SUPABASE_ANON_KEY === 'your_supabase_anon_key_here') {
        // Return demo message when Supabase is not configured
        return reply.send(`
          <div class="p-4 bg-yellow-900/20 border border-yellow-800 rounded-lg">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"/>
              </svg>
              <span class="text-yellow-200">Please configure Supabase credentials to enable video syncing</span>
            </div>
          </div>
        `);
      }

      const result = await syncVideos();

      return reply.view('partials/video-list', {
        videos: result.videos,
        layout: false
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.code(500).send(`
        <div class="p-4 bg-red-900/20 border border-red-800 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
            </svg>
            <span class="text-red-200">Sync failed. Please check your configuration.</span>
          </div>
        </div>
      `);
    }
  });
}

export default videoRoutes;