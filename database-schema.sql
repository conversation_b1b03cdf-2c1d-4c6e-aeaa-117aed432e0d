-- =====================================================
-- VELIO APP DATABASE SCHEMA
-- Run this in your Supabase SQL Editor
-- =====================================================

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- =====================================================
-- CHANNELS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.channels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    channel_id TEXT NOT NULL UNIQUE,
    url TEXT NOT NULL,
    description TEXT,
    subscriber_count INTEGER DEFAULT 0,
    is_tracked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- VIDEOS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    url TEXT NOT NULL UNIQUE,
    video_id TEXT,
    thumbnail_url TEXT,
    channel_name TEXT NOT NULL,
    channel_id TEXT NOT NULL,
    published_at TIMESTAMP WITH TIME ZONE,
    duration TEXT,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraint
    CONSTRAINT fk_videos_channel_id 
        FOREIGN KEY (channel_id) 
        REFERENCES channels(channel_id) 
        ON DELETE CASCADE
);

-- =====================================================
-- USERS TABLE (if you want user management)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SAVED VIDEOS TABLE (for user favorites)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.saved_videos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    video_id UUID REFERENCES videos(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique combination
    UNIQUE(user_id, video_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id);
CREATE INDEX IF NOT EXISTS idx_videos_published_at ON videos(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_url ON videos(url);
CREATE INDEX IF NOT EXISTS idx_channels_tracked ON channels(is_tracked) WHERE is_tracked = true;
CREATE INDEX IF NOT EXISTS idx_channels_channel_id ON channels(channel_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_user_id ON saved_videos(user_id);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_videos ENABLE ROW LEVEL SECURITY;

-- Channels policies
CREATE POLICY "Allow public read access on channels" ON channels
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to manage channels" ON channels
    FOR ALL USING (auth.role() = 'authenticated');

-- Videos policies
CREATE POLICY "Allow public read access on videos" ON videos
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to manage videos" ON videos
    FOR ALL USING (auth.role() = 'authenticated');

-- Users policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);

-- Saved videos policies
CREATE POLICY "Users can manage their own saved videos" ON saved_videos
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- FUNCTIONS
-- =====================================================

-- Function to execute SQL (for database initialization from app)
CREATE OR REPLACE FUNCTION execute_sql(sql text)
RETURNS text AS $$
BEGIN
    EXECUTE sql;
    RETURN 'Success';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS FOR AUTO-UPDATING TIMESTAMPS
-- =====================================================

-- Channels table trigger
DROP TRIGGER IF EXISTS update_channels_updated_at ON channels;
CREATE TRIGGER update_channels_updated_at
    BEFORE UPDATE ON channels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Videos table trigger
DROP TRIGGER IF EXISTS update_videos_updated_at ON videos;
CREATE TRIGGER update_videos_updated_at
    BEFORE UPDATE ON videos
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Users table trigger
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Insert sample channels
INSERT INTO channels (name, channel_id, url, description, is_tracked) 
VALUES 
    ('Tech Review Channel', 'UC_tech_review_123', 'https://www.youtube.com/@techreview', 'Latest tech reviews and tutorials', true),
    ('Cooking Masters', 'UC_cooking_456', 'https://www.youtube.com/@cookingmasters', 'Delicious recipes and cooking tips', true)
ON CONFLICT (channel_id) DO NOTHING;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT ALL ON channels TO authenticated;
GRANT ALL ON videos TO authenticated;
GRANT ALL ON users TO authenticated;
GRANT ALL ON saved_videos TO authenticated;

-- Grant permissions to anonymous users (read-only)
GRANT SELECT ON channels TO anon;
GRANT SELECT ON videos TO anon;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Uncomment these to verify your setup:
-- SELECT 'Channels table created successfully' AS status WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'channels');
-- SELECT 'Videos table created successfully' AS status WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'videos');
-- SELECT COUNT(*) AS channel_count FROM channels;
-- SELECT COUNT(*) AS video_count FROM videos;

-- =====================================================
-- CLEANUP (if needed)
-- =====================================================

-- Uncomment these if you need to reset the database:
-- DROP TABLE IF EXISTS saved_videos CASCADE;
-- DROP TABLE IF EXISTS videos CASCADE;
-- DROP TABLE IF EXISTS channels CASCADE;
-- DROP TABLE IF EXISTS users CASCADE;
-- DROP FUNCTION IF EXISTS execute_sql(text);
-- DROP FUNCTION IF EXISTS update_updated_at_column();
