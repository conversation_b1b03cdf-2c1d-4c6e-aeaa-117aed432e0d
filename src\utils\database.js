"use strict";

import { supabase } from './supabase.js';

export async function initializeDatabase() {
  try {
    console.log('🔄 Initializing database...');

    // Check if we're using real Supabase
    if (process.env.SUPABASE_URL === 'https://your-project.supabase.co' ||
        process.env.SUPABASE_ANON_KEY === 'your_supabase_anon_key_here') {
      console.log('⚠️  Supabase not configured - skipping database initialization');
      return;
    }

    // Create tables if they don't exist
    await createTables();
    console.log('✅ Database initialized successfully');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    if (process.env.NODE_ENV === 'production') {
      throw error;
    }
  }
}

async function createTables() {
  // Create channels table
  const { error: channelsError } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS channels (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        channel_id TEXT NOT NULL UNIQUE,
        url TEXT NOT NULL,
        description TEXT,
        subscriber_count INTEGER,
        is_tracked BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (channelsError && !channelsError.message.includes('already exists')) {
    console.error('Error creating channels table:', channelsError);
  }

  // Create videos table
  const { error: videosError } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS videos (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        url TEXT NOT NULL UNIQUE,
        video_id TEXT,
        thumbnail_url TEXT,
        channel_name TEXT NOT NULL,
        channel_id TEXT NOT NULL,
        published_at TIMESTAMP WITH TIME ZONE,
        duration TEXT,
        view_count INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (videosError && !videosError.message.includes('already exists')) {
    console.error('Error creating videos table:', videosError);
  }

  // Create users table
  const { error: usersError } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (usersError && !usersError.message.includes('already exists')) {
    console.error('Error creating users table:', usersError);
  }

  // Create indexes for performance
  await createIndexes();
}

async function createIndexes() {
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id)',
    'CREATE INDEX IF NOT EXISTS idx_videos_published_at ON videos(published_at DESC)',
    'CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at DESC)',
    'CREATE INDEX IF NOT EXISTS idx_channels_tracked ON channels(is_tracked)',
    'CREATE INDEX IF NOT EXISTS idx_videos_url ON videos(url)',
    'CREATE INDEX IF NOT EXISTS idx_channels_channel_id ON channels(channel_id)'
  ];

  for (const indexSql of indexes) {
    const { error } = await supabase.rpc('execute_sql', { sql: indexSql });
    if (error && !error.message.includes('already exists')) {
      console.warn('Index creation warning:', error.message);
    }
  }
}
