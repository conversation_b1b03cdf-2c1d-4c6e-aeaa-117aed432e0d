"use strict";

import { supabase } from './supabase.js';

/**
 * Upload a file to Supabase Storage
 * @param {string} bucket - Storage bucket name
 * @param {string} filePath - File path in bucket
 * @param {Buffer} fileBuffer - File data
 * @param {string} contentType - MIME type
 * @returns {Promise<string>} - Public URL of uploaded file
 */
export async function uploadFile(bucket, filePath, fileBuffer, contentType) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, fileBuffer, {
        contentType,
        upsert: true
      });

    if (error) throw error;

    // Get public URL
    const { data: publicData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    return publicData.publicUrl;
  } catch (error) {
    console.error('File upload error:', error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
}

/**
 * Delete a file from Supabase Storage
 * @param {string} bucket - Storage bucket name
 * @param {string} fileUrl - Full URL or file path
 * @returns {Promise<void>}
 */
export async function deleteFile(bucket, fileUrl) {
  try {
    // Extract file path from URL if needed
    let filePath = fileUrl;
    if (fileUrl.includes('supabase.co') || fileUrl.includes('supabase.com')) {
      const urlParts = fileUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === bucket);
      if (bucketIndex !== -1) {
        filePath = urlParts.slice(bucketIndex + 1).join('/');
      }
    }

    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);

    if (error) throw error;
  } catch (error) {
    console.error('File deletion error:', error);
    throw new Error(`Failed to delete file: ${error.message}`);
  }
}

/**
 * Get a signed URL for a private file
 * @param {string} bucket - Storage bucket name
 * @param {string} filePath - File path in bucket
 * @param {number} expiresIn - Expiration time in seconds
 * @returns {Promise<string>} - Signed URL
 */
export async function getSignedUrl(bucket, filePath, expiresIn = 3600) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) throw error;

    return data.signedUrl;
  } catch (error) {
    console.error('Signed URL error:', error);
    throw new Error(`Failed to create signed URL: ${error.message}`);
  }
}

/**
 * List files in a bucket folder
 * @param {string} bucket - Storage bucket name
 * @param {string} folder - Folder path
 * @returns {Promise<Array>} - Array of file objects
 */
export async function listFiles(bucket, folder = '') {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(folder);

    if (error) throw error;

    return data || [];
  } catch (error) {
    console.error('List files error:', error);
    throw new Error(`Failed to list files: ${error.message}`);
  }
}

/**
 * Validate image file
 * @param {Object} file - File object from fastify
 * @returns {Object} - Validation result
 */
export function validateImageFile(file) {
  const errors = [];
  
  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('File size must be less than 5MB');
  }

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.mimetype)) {
    errors.push('File must be a JPEG, PNG, or WebP image');
  }

  // Check file name
  if (!file.filename || file.filename.length > 255) {
    errors.push('Invalid file name');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Generate a unique file name
 * @param {string} originalName - Original file name
 * @param {string} userId - User ID for folder structure
 * @returns {string} - Unique file name
 */
export function generateFileName(originalName, userId) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop().toLowerCase();
  
  return `${userId}/${timestamp}-${random}.${extension}`;
}

/**
 * Resize image using sharp (if available) or return buffer as-is
 * @param {Buffer} buffer - Image buffer
 * @param {Object} options - Resize options
 * @returns {Promise<Buffer>} - Resized image buffer
 */
export async function resizeImage(buffer, options = {}) {
  try {
    // Try to use sharp for image processing if available
    const sharp = await import('sharp').catch(() => null);
    
    if (!sharp) {
      console.warn('Sharp not available, returning original image');
      return buffer;
    }

    const { width = 400, height = 400, quality = 80 } = options;
    
    return await sharp.default(buffer)
      .resize(width, height, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality })
      .toBuffer();
  } catch (error) {
    console.warn('Image resize failed, returning original:', error.message);
    return buffer;
  }
}

/**
 * Get file info from storage
 * @param {string} bucket - Storage bucket name
 * @param {string} filePath - File path in bucket
 * @returns {Promise<Object>} - File info
 */
export async function getFileInfo(bucket, filePath) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .getPublicUrl(filePath);

    if (error) throw error;

    return {
      publicUrl: data.publicUrl,
      path: filePath,
      bucket
    };
  } catch (error) {
    console.error('Get file info error:', error);
    throw new Error(`Failed to get file info: ${error.message}`);
  }
}
