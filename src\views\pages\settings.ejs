<div class="max-w-4xl mx-auto">
  <h1 class="text-3xl font-bold text-white mb-6 hidden lg:block">Settings</h1>

  <div class="space-y-6">
    <!-- User Profile Section -->
    <div class="bg-gray-800 rounded-lg border border-gray-700 p-6">
      <h2 class="text-lg font-semibold text-white mb-4">Profile</h2>

      <!-- Profile Loading State -->
      <div id="profile-loading" class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        <span class="ml-3 text-gray-400">Loading profile...</span>
      </div>

      <!-- Profile Content -->
      <div id="profile-content" class="hidden">
        <!-- Profile Header -->
        <div class="flex items-start space-x-6 mb-6">
          <!-- Profile Image -->
          <div class="relative">
            <div class="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
              <img id="profile-avatar" class="w-full h-full object-cover hidden" alt="Profile">
              <div id="default-avatar" class="w-12 h-12 text-gray-400">
                <svg fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
            </div>

            <!-- Upload Button -->
            <button
              id="upload-avatar-btn"
              class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-600 hover:bg-green-700 rounded-full flex items-center justify-center text-white transition-colors"
              title="Change profile picture"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </button>

            <!-- Hidden file input -->
            <input type="file" id="avatar-upload" class="hidden" accept="image/jpeg,image/png,image/webp">
          </div>

          <!-- Profile Info -->
          <div class="flex-1">
            <div class="space-y-4">
              <!-- Full Name -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                <div class="flex items-center space-x-2">
                  <input
                    type="text"
                    id="profile-name"
                    class="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your full name"
                  >
                  <button
                    id="save-name-btn"
                    class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                    disabled
                  >
                    Save
                  </button>
                </div>
                <div id="name-error" class="text-red-400 text-sm mt-1 hidden"></div>
              </div>

              <!-- Email (read-only) -->
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <input
                  type="email"
                  id="profile-email"
                  class="w-full px-3 py-2 bg-gray-600 border border-gray-600 rounded-lg text-gray-300 cursor-not-allowed"
                  readonly
                  placeholder="Loading..."
                >
                <p class="text-xs text-gray-500 mt-1">Email cannot be changed</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Sign Out Section -->
        <div class="border-t border-gray-700 pt-6">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-white">Sign Out</h3>
              <p class="text-sm text-gray-400">Sign out of your VirSnapp account</p>
            </div>
            <button
              id="signout-btn"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-lg transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div id="profile-error" class="hidden p-4 bg-red-900/20 border border-red-800 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
          </svg>
          <span id="profile-error-text" class="text-red-200"></span>
        </div>
      </div>
    </div>


  </div>
</div>

<!-- Sign Out Confirmation Modal -->
<div id="signout-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
    <h3 class="text-lg font-semibold text-white mb-4">Confirm Sign Out</h3>
    <p class="text-gray-300 mb-6">Are you sure you want to sign out of your account?</p>
    <div class="flex space-x-3">
      <button
        id="confirm-signout"
        class="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors"
      >
        Sign Out
      </button>
      <button
        id="cancel-signout"
        class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
      >
        Cancel
      </button>
    </div>
  </div>
</div>

<!-- Toast Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Profile management
  const profileLoading = document.getElementById('profile-loading');
  const profileContent = document.getElementById('profile-content');
  const profileError = document.getElementById('profile-error');
  const profileErrorText = document.getElementById('profile-error-text');

  const profileAvatar = document.getElementById('profile-avatar');
  const defaultAvatar = document.getElementById('default-avatar');
  const profileName = document.getElementById('profile-name');
  const profileEmail = document.getElementById('profile-email');
  const saveNameBtn = document.getElementById('save-name-btn');
  const nameError = document.getElementById('name-error');

  const uploadAvatarBtn = document.getElementById('upload-avatar-btn');
  const avatarUpload = document.getElementById('avatar-upload');

  const signoutBtn = document.getElementById('signout-btn');
  const signoutModal = document.getElementById('signout-modal');
  const confirmSignout = document.getElementById('confirm-signout');
  const cancelSignout = document.getElementById('cancel-signout');

  let currentProfile = null;
  let originalName = '';
  let profileCache = null;
  let cacheTimestamp = null;
  const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes

  // Check if cached profile is still valid
  function isCacheValid() {
    return profileCache && cacheTimestamp && (Date.now() - cacheTimestamp < CACHE_DURATION);
  }

  // Load user profile with client-side caching
  async function loadProfile(forceRefresh = false) {
    try {
      // Use cache if valid and not forcing refresh
      if (!forceRefresh && isCacheValid()) {
        console.log('Using cached profile data');
        currentProfile = profileCache.profile;
        displayProfile(profileCache.user, profileCache.profile);
        profileLoading.classList.add('hidden');
        profileContent.classList.remove('hidden');
        return;
      }

      const response = await fetch('/auth/profile');
      const data = await response.json();

      if (response.ok) {
        // Cache the response
        profileCache = data;
        cacheTimestamp = Date.now();

        currentProfile = data.profile;
        displayProfile(data.user, data.profile);
        profileLoading.classList.add('hidden');
        profileContent.classList.remove('hidden');
      } else {
        throw new Error(data.error || 'Failed to load profile');
      }
    } catch (error) {
      showProfileError(error.message);
      profileLoading.classList.add('hidden');
    }
  }

  // Display profile data
  function displayProfile(user, profile) {
    profileEmail.value = user.email;
    profileName.value = profile?.full_name || '';
    originalName = profile?.full_name || '';

    if (profile?.avatar_url) {
      profileAvatar.src = profile.avatar_url;
      profileAvatar.classList.remove('hidden');
      defaultAvatar.classList.add('hidden');
    } else {
      profileAvatar.classList.add('hidden');
      defaultAvatar.classList.remove('hidden');
    }

    updateSaveButton();
  }

  // Update save button state
  function updateSaveButton() {
    const hasChanges = profileName.value.trim() !== originalName;
    saveNameBtn.disabled = !hasChanges || !profileName.value.trim();
  }

  // Save profile name
  async function saveName() {
    const newName = profileName.value.trim();

    if (!newName) {
      showNameError('Name is required');
      return;
    }

    if (newName.length < 2) {
      showNameError('Name must be at least 2 characters');
      return;
    }

    try {
      saveNameBtn.disabled = true;
      saveNameBtn.textContent = 'Saving...';

      const response = await fetch('/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ fullName: newName })
      });

      const data = await response.json();

      if (response.ok) {
        originalName = newName;
        currentProfile = data.profile;

        // Invalidate cache and update with new data
        profileCache = { user: profileCache?.user || {}, profile: data.profile };
        cacheTimestamp = Date.now();

        updateSaveButton();
        hideNameError();
        showToast('Profile updated successfully', 'success');
      } else {
        throw new Error(data.error || 'Failed to update profile');
      }
    } catch (error) {
      showNameError(error.message);
    } finally {
      saveNameBtn.disabled = false;
      saveNameBtn.textContent = 'Save';
    }
  }

  // Upload avatar
  async function uploadAvatar(file) {
    if (!file) return;

    // Validate file
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      showToast('Please select a JPG, PNG, or WebP image', 'error');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      showToast('Image must be smaller than 5MB', 'error');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/auth/avatar', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        profileAvatar.src = data.avatarUrl;
        profileAvatar.classList.remove('hidden');
        defaultAvatar.classList.add('hidden');
        showToast('Profile picture updated successfully', 'success');
      } else {
        throw new Error(data.error || 'Failed to upload image');
      }
    } catch (error) {
      showToast(error.message, 'error');
    }
  }

  // Sign out
  async function signOut() {
    try {
      const response = await fetch('/auth/logout', {
        method: 'POST'
      });

      if (response.ok) {
        window.location.href = '/auth/signin';
      } else {
        throw new Error('Failed to sign out');
      }
    } catch (error) {
      showToast(error.message, 'error');
    }
  }

  // Event listeners
  profileName.addEventListener('input', updateSaveButton);
  saveNameBtn.addEventListener('click', saveName);

  uploadAvatarBtn.addEventListener('click', () => avatarUpload.click());
  avatarUpload.addEventListener('change', (e) => {
    if (e.target.files[0]) {
      uploadAvatar(e.target.files[0]);
    }
  });

  signoutBtn.addEventListener('click', () => {
    signoutModal.classList.remove('hidden');
  });

  confirmSignout.addEventListener('click', () => {
    signoutModal.classList.add('hidden');
    signOut();
  });

  cancelSignout.addEventListener('click', () => {
    signoutModal.classList.add('hidden');
  });

  // Close modal on outside click
  signoutModal.addEventListener('click', (e) => {
    if (e.target === signoutModal) {
      signoutModal.classList.add('hidden');
    }
  });

  // Helper functions
  function showProfileError(message) {
    profileErrorText.textContent = message;
    profileError.classList.remove('hidden');
  }

  function showNameError(message) {
    nameError.textContent = message;
    nameError.classList.remove('hidden');
  }

  function hideNameError() {
    nameError.classList.add('hidden');
  }

  function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `p-4 rounded-lg shadow-lg max-w-sm ${
      type === 'success' ? 'bg-green-900/20 border border-green-800 text-green-200' :
      type === 'error' ? 'bg-red-900/20 border border-red-800 text-red-200' :
      'bg-blue-900/20 border border-blue-800 text-blue-200'
    }`;

    toast.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          ${type === 'success' ?
            '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>' :
            type === 'error' ?
            '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>' :
            '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"/>'
          }
        </svg>
        <span>${message}</span>
      </div>
    `;

    document.getElementById('toast-container').appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 5000);
  }

  // Initialize
  loadProfile();
});
</script>