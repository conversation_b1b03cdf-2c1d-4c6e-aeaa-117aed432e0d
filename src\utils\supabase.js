"use strict";

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

let supabase, supabaseAdmin;

if (!supabaseUrl || !supabaseAnonKey ||
    supabaseUrl === 'https://your-project.supabase.co' ||
    supabaseAnonKey === 'your_supabase_anon_key_here') {
  console.warn('⚠️  Supabase not configured - using placeholder values. Configure SUPABASE_URL and SUPABASE_ANON_KEY in .env for full functionality.');
  // Create a dummy client that won't actually work but won't crash the app
  supabase = {
    from: () => ({
      select: () => ({ data: [], error: new Error('Supabase not configured') }),
      insert: () => ({ data: null, error: new Error('Supabase not configured') }),
      upsert: () => ({ data: null, error: new Error('Supabase not configured') })
    }),
    auth: {
      signInWithPassword: () => ({ data: null, error: new Error('Supabase not configured') })
    }
  };
  supabaseAdmin = supabase;
} else {
  // Client for public operations (auth, etc.)
  supabase = createClient(supabaseUrl, supabaseAnonKey);

  // Admin client for server-side operations (bypasses RLS)
  supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export { supabase, supabaseAdmin };

// Database schema helpers
export const createTables = async () => {
  try {
    // Create videos table
    await supabase.sql`
      CREATE TABLE IF NOT EXISTS videos (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        url TEXT NOT NULL UNIQUE,
        thumbnail_url TEXT,
        channel_name TEXT NOT NULL,
        channel_id TEXT NOT NULL,
        published_at TIMESTAMP WITH TIME ZONE,
        duration TEXT,
        view_count INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create channels table
    await supabase.sql`
      CREATE TABLE IF NOT EXISTS channels (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        channel_id TEXT NOT NULL UNIQUE,
        url TEXT NOT NULL,
        description TEXT,
        subscriber_count INTEGER,
        is_tracked BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create users table
    await supabase.sql`
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create user profiles table
    await supabase.sql`
      CREATE TABLE IF NOT EXISTS user_profiles (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
        full_name TEXT,
        bio TEXT,
        avatar_url TEXT,
        preferences JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id)
      );
    `;

    // Create RLS policies for user profiles
    await supabase.sql`
      ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

      CREATE POLICY "Users can view own profile" ON user_profiles
        FOR SELECT USING (auth.uid() = user_id);

      CREATE POLICY "Users can update own profile" ON user_profiles
        FOR UPDATE USING (auth.uid() = user_id);

      CREATE POLICY "Users can insert own profile" ON user_profiles
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    `;

    // Create storage bucket for avatars
    await supabase.sql`
      INSERT INTO storage.buckets (id, name, public)
      VALUES ('avatars', 'avatars', true)
      ON CONFLICT (id) DO NOTHING;
    `;

    // Create storage policies for avatars
    await supabase.sql`
      CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
        FOR SELECT USING (bucket_id = 'avatars');

      CREATE POLICY "Users can upload their own avatar" ON storage.objects
        FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

      CREATE POLICY "Users can update their own avatar" ON storage.objects
        FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

      CREATE POLICY "Users can delete their own avatar" ON storage.objects
        FOR DELETE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
    `;

    console.log('Database tables created successfully');
  } catch (error) {
    console.error('Error creating tables:', error);
  }
};