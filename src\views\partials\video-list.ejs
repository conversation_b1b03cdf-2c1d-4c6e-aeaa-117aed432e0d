<% if (videos && videos.length > 0) { %>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <% videos.forEach(video => { %>
      <%- include('./video-card', { video }) %>
    <% }); %>
  </div>
<% } else { %>
  <div class="text-center py-12">
    <svg class="w-12 h-12 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
    </svg>
    <h3 class="text-lg font-medium text-white mb-2">No videos found</h3>
    <p class="text-gray-400">Try syncing to fetch the latest videos.</p>
  </div>
<% } %>