"use strict";

import { supabase } from '../utils/supabase.js';

async function authRoutes(fastify, options) {
  // Register JWT decode hook
  fastify.decorate('authenticate', async function (request, reply) {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.send(err);
    }
  });

  // Login route
  fastify.post('/auth/login', async (request, reply) => {
    const { email, password } = request.body;

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      const token = fastify.jwt.sign({ 
        userId: data.user.id,
        email: data.user.email 
      });

      reply.setCookie('token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      return { success: true, user: data.user };
    } catch (error) {
      reply.code(401);
      return { error: 'Invalid credentials' };
    }
  });

  // Logout route
  fastify.post('/auth/logout', async (request, reply) => {
    reply.clearCookie('token');
    return { success: true };
  });

  // Protected route example
  fastify.get('/auth/profile', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    return { user: request.user };
  });
}

export default authRoutes;