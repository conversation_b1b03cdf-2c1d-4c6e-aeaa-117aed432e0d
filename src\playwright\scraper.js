"use strict";

import { chromium } from 'playwright';
import { supabase } from '../utils/supabase.js';

export async function scrapeYouTubeChannel(channelUrl) {
  const browser = await chromium.launch({
    headless: true
  });

  try {
    const page = await browser.newPage();
    await page.goto(channelUrl + '/videos');
    
    // Wait for video elements to load
    await page.waitForSelector('div#contents ytd-rich-item-renderer', { timeout: 10000 });

    // Extract video data
    const videos = await page.evaluate(() => {
      const videoElements = document.querySelectorAll('div#contents ytd-rich-item-renderer');
      const results = [];

      videoElements.forEach((element, index) => {
        if (index >= 20) return; // Limit to 20 videos

        try {
          const titleElement = element.querySelector('#video-title');
          const thumbnailElement = element.querySelector('img');
          const metadataElement = element.querySelector('#metadata-line span');
          
          if (titleElement && thumbnailElement) {
            results.push({
              title: titleElement.textContent.trim(),
              url: titleElement.href,
              thumbnail_url: thumbnailElement.src,
              channel_name: document.querySelector('yt-formatted-string.ytd-channel-name')?.textContent?.trim() || 'Unknown',
              published_at: metadataElement?.textContent?.trim() || null
            });
          }
        } catch (error) {
          console.error('Error extracting video data:', error);
        }
      });

      return results;
    });

    return videos;
  } catch (error) {
    console.error('Error scraping YouTube channel:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export async function scrapeAndStore(channelUrl, channelId) {
  try {
    const videos = await scrapeYouTubeChannel(channelUrl);
    
    for (const video of videos) {
      const videoData = {
        ...video,
        channel_id: channelId
      };

      const { error } = await supabase
        .from('videos')
        .upsert(videoData, { onConflict: 'url' });

      if (error) {
        console.error('Error storing video:', error);
      }
    }

    return videos;
  } catch (error) {
    console.error('Error in scrapeAndStore:', error);
    throw error;
  }
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const channelUrl = process.argv[2];
  const channelId = process.argv[3];
  
  if (!channelUrl || !channelId) {
    console.error('Usage: node scraper.js <channel_url> <channel_id>');
    process.exit(1);
  }

  scrapeAndStore(channelUrl, channelId)
    .then(videos => {
      console.log(`Scraped ${videos.length} videos successfully`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Scraping failed:', error);
      process.exit(1);
    });
}