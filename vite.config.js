import { defineConfig } from 'vite';
import Icons from 'unplugin-icons/vite';

export default defineConfig({
  plugins: [
    Icons({
      compiler: 'raw',
      autoInstall: true,
      iconCustomizer(collection, icon, props) {
        if (collection === 'mdi' || collection === 'lucide') {
          props.width = props.width || '1em';
          props.height = props.height || '1em';
        }
      }
    })
  ],
  build: {
    rollupOptions: {
      input: 'src/frontend/main.js',
      output: {
        dir: 'public/js',
        entryFileNames: 'app.min.js',
        format: 'iife'
      }
    }
  }
});