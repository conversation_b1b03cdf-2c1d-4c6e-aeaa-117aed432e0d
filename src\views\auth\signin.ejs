<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - VirSnapp</title>
  <link href="/public/css/style.css" rel="stylesheet">
  <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  <script src="/public/js/app.min.js" defer></script>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen flex items-center justify-center">
  <div class="w-full max-w-md">
    <!-- Logo Section -->
    <div class="text-center mb-8">
      <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-white">VirSnapp</h1>
      <p class="text-gray-400 mt-2">YouTube Channel Tracker</p>
    </div>

    <!-- Main Content -->
    <div class="bg-gray-800 rounded-xl shadow-2xl p-8 border border-gray-700">
      <div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h2 class="text-2xl font-bold text-white mb-2">Welcome back</h2>
    <p class="text-gray-400">Sign in to your account to continue</p>
  </div>

  <!-- Error Message Container -->
  <div id="error-message" class="hidden p-4 bg-red-900/20 border border-red-800 rounded-lg">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
      </svg>
      <span id="error-text" class="text-red-200"></span>
    </div>
  </div>

  <!-- Success Message Container -->
  <div id="success-message" class="hidden p-4 bg-green-900/20 border border-green-800 rounded-lg">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
      </svg>
      <span id="success-text" class="text-green-200"></span>
    </div>
  </div>

  <!-- Sign In Form -->
  <form 
    hx-post="/auth/login" 
    hx-target="#form-response"
    hx-indicator="#loading-indicator"
    class="space-y-5"
    id="signin-form"
  >
    <!-- Email Field -->
    <div>
      <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
        Email address
      </label>
      <input 
        type="email" 
        id="email" 
        name="email" 
        required
        autocomplete="email"
        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
        placeholder="Enter your email"
      >
      <div class="text-red-400 text-sm mt-1 hidden" id="email-error"></div>
    </div>

    <!-- Password Field -->
    <div>
      <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
        Password
      </label>
      <div class="relative">
        <input 
          type="password" 
          id="password" 
          name="password" 
          required
          autocomplete="current-password"
          class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors pr-12"
          placeholder="Enter your password"
        >
        <button 
          type="button" 
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
          onclick="togglePassword()"
        >
          <svg class="w-5 h-5 text-gray-400 hover:text-gray-300" id="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </button>
      </div>
      <div class="text-red-400 text-sm mt-1 hidden" id="password-error"></div>
    </div>

    <!-- Remember Me & Forgot Password -->
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input 
          type="checkbox" 
          id="remember" 
          name="remember" 
          class="w-4 h-4 bg-gray-700 border border-gray-600 rounded text-green-500 focus:ring-green-500 focus:ring-2"
        >
        <label for="remember" class="ml-2 text-sm text-gray-300">
          Remember me
        </label>
      </div>
      <a href="/auth/forgot-password" class="text-sm text-green-400 hover:text-green-300 transition-colors">
        Forgot password?
      </a>
    </div>

    <!-- Submit Button -->
    <button 
      type="submit" 
      class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
      id="submit-button"
    >
      <span class="flex items-center justify-center">
        <span id="button-text">Sign in</span>
        <div id="loading-indicator" class="hidden ml-2">
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        </div>
      </span>
    </button>
  </form>

  <!-- Divider -->
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-700"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span class="px-4 bg-gray-800 text-gray-400">or</span>
    </div>
  </div>

  <!-- Sign Up Link -->
  <div class="text-center">
    <p class="text-gray-400">
      Don't have an account? 
      <a href="/auth/signup" class="text-green-400 hover:text-green-300 font-medium transition-colors">
        Sign up
      </a>
    </p>
  </div>
</div>

<!-- Response Container -->
<div id="form-response"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('signin-form');
  const submitButton = document.getElementById('submit-button');
  const buttonText = document.getElementById('button-text');
  const loadingIndicator = document.getElementById('loading-indicator');
  const errorMessage = document.getElementById('error-message');
  const successMessage = document.getElementById('success-message');
  const errorText = document.getElementById('error-text');
  const successText = document.getElementById('success-text');

  // Handle form submission
  form.addEventListener('htmx:beforeRequest', function() {
    hideMessages();
    setLoading(true);
  });

  form.addEventListener('htmx:afterRequest', function(event) {
    setLoading(false);
    
    if (event.detail.xhr.status === 200) {
      const response = JSON.parse(event.detail.xhr.responseText);
      
      if (response.success) {
        showSuccess('Sign in successful! Redirecting...');
        setTimeout(() => {
          window.location.href = response.redirect || '/';
        }, 1000);
      } else {
        showError(response.error || 'Sign in failed');
      }
    } else {
      const response = JSON.parse(event.detail.xhr.responseText);
      showError(response.error || 'An error occurred');
    }
  });

  form.addEventListener('htmx:responseError', function() {
    setLoading(false);
    showError('Network error. Please try again.');
  });

  // Real-time validation
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');

  emailInput.addEventListener('blur', validateEmail);
  passwordInput.addEventListener('blur', validatePassword);

  function validateEmail() {
    const email = emailInput.value;
    const emailError = document.getElementById('email-error');
    
    if (!email) {
      showFieldError('email-error', 'Email is required');
      return false;
    }
    
    if (!isValidEmail(email)) {
      showFieldError('email-error', 'Please enter a valid email address');
      return false;
    }
    
    hideFieldError('email-error');
    return true;
  }

  function validatePassword() {
    const password = passwordInput.value;
    const passwordError = document.getElementById('password-error');
    
    if (!password) {
      showFieldError('password-error', 'Password is required');
      return false;
    }
    
    if (password.length < 6) {
      showFieldError('password-error', 'Password must be at least 6 characters');
      return false;
    }
    
    hideFieldError('password-error');
    return true;
  }

  function setLoading(loading) {
    submitButton.disabled = loading;
    if (loading) {
      buttonText.textContent = 'Signing in...';
      loadingIndicator.classList.remove('hidden');
    } else {
      buttonText.textContent = 'Sign in';
      loadingIndicator.classList.add('hidden');
    }
  }

  function showError(message) {
    errorText.textContent = message;
    errorMessage.classList.remove('hidden');
    successMessage.classList.add('hidden');
  }

  function showSuccess(message) {
    successText.textContent = message;
    successMessage.classList.remove('hidden');
    errorMessage.classList.add('hidden');
  }

  function hideMessages() {
    errorMessage.classList.add('hidden');
    successMessage.classList.add('hidden');
  }

  function showFieldError(elementId, message) {
    const element = document.getElementById(elementId);
    element.textContent = message;
    element.classList.remove('hidden');
  }

  function hideFieldError(elementId) {
    const element = document.getElementById(elementId);
    element.classList.add('hidden');
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
});

function togglePassword() {
  const passwordInput = document.getElementById('password');
  const eyeIcon = document.getElementById('eye-icon');
  
  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    eyeIcon.innerHTML = `
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
    `;
  } else {
    passwordInput.type = 'password';
    eyeIcon.innerHTML = `
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
    `;
  }
}
</script>
    </div>

    <!-- Footer -->
    <div class="text-center mt-8 text-gray-500 text-sm">
      <p>&copy; 2024 VirSnapp. All rights reserved.</p>
    </div>
  </div>

  <!-- Toast Container -->
  <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

  <!-- Loading Overlay -->
  <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      <span class="text-white">Loading...</span>
    </div>
  </div>
</body>
</html>
