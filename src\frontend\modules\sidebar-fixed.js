"use strict";

export function initSidebar() {
  const sidebar = document.getElementById('sidebar');
  const collapseButton = document.getElementById('sidebar-collapse-button');
  const sidebarTexts = document.querySelectorAll('.sidebar-text');
  
  if (!sidebar || !collapseButton) return;

  // Initialize the sidebar state immediately based on saved preferences
  const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
  
  // Apply the initial state without animation to prevent visual flash
  sidebar.style.transition = 'none';
  
  if (isCollapsed) {
    applySidebarState(true);
  } else {
    applySidebarState(false);
  }
  
  // Re-enable transitions after initial state is set
  requestAnimationFrame(() => {
    sidebar.style.transition = '';
  });

  // Event listener for toggle button
  collapseButton.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    const currentlyCollapsed = sidebar.classList.contains('w-16');
    toggleSidebar(!currentlyCollapsed);
  });

  // Handle window resize to ensure proper state on mobile/desktop switches
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      // Only apply saved state on desktop
      if (window.innerWidth >= 1024) {
        const savedCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        applySidebarState(savedCollapsed);
      }
    }, 150);
  });

  function toggleSidebar(shouldCollapse) {
    applySidebarState(shouldCollapse);
    localStorage.setItem('sidebar-collapsed', shouldCollapse.toString());
  }

  function applySidebarState(isCollapsed) {
    const navItems = sidebar.querySelectorAll('.nav-item');
    const logoHeader = sidebar.querySelector('div:first-child');
    const buttonIcon = collapseButton.querySelector('svg');

    if (isCollapsed) {
      // Apply collapsed state
      sidebar.classList.remove('w-64');
      sidebar.classList.add('w-16');
      
      // Hide all text elements
      sidebarTexts.forEach(text => {
        text.classList.add('hidden');
      });
      
      // Update navigation items for collapsed state
      navItems.forEach(item => {
        item.classList.add('justify-center');
        item.classList.remove('px-3');
        item.classList.add('px-2');
        
        const iconContainer = item.querySelector('.nav-icon-container');
        const icon = item.querySelector('.nav-icon-container svg');
        
        if (iconContainer) {
          iconContainer.classList.remove('w-5', 'h-5');
          iconContainer.classList.add('w-8', 'h-8');
        }
        
        if (icon) {
          // Keep icon size consistent but container is larger
          icon.classList.remove('w-6', 'h-6');
          icon.classList.add('w-5', 'h-5');
        }
        
        const textSpan = item.querySelector('.sidebar-text');
        if (textSpan) {
          textSpan.classList.add('hidden');
          textSpan.classList.remove('ml-3');
        }
      });
      
      // Center the logo in the header
      if (logoHeader) {
        logoHeader.classList.remove('justify-between');
        logoHeader.classList.add('justify-center');
      }
      
      // Rotate collapse button icon
      if (buttonIcon) {
        buttonIcon.style.transform = 'rotate(180deg)';
      }
      
    } else {
      // Apply expanded state
      sidebar.classList.remove('w-16');
      sidebar.classList.add('w-64');
      
      // Show all text elements
      sidebarTexts.forEach(text => {
        text.classList.remove('hidden');
      });
      
      // Reset navigation items for expanded state
      navItems.forEach(item => {
        item.classList.remove('justify-center');
        item.classList.remove('px-2');
        item.classList.add('px-3');
        
        const iconContainer = item.querySelector('.nav-icon-container');
        const icon = item.querySelector('.nav-icon-container svg');
        
        if (iconContainer) {
          iconContainer.classList.remove('w-8', 'h-8');
          iconContainer.classList.add('w-5', 'h-5');
        }
        
        if (icon) {
          icon.classList.remove('w-6', 'h-6');
          icon.classList.add('w-5', 'h-5');
        }
        
        const textSpan = item.querySelector('.sidebar-text');
        if (textSpan) {
          textSpan.classList.remove('hidden');
          textSpan.classList.add('ml-3');
        }
      });
      
      // Reset logo header layout
      if (logoHeader) {
        logoHeader.classList.remove('justify-center');
        logoHeader.classList.add('justify-between');
      }
      
      // Reset collapse button icon
      if (buttonIcon) {
        buttonIcon.style.transform = 'rotate(0deg)';
      }
    }
  }

  // Expose functions for debugging (development only)
  if (process.env.NODE_ENV === 'development') {
    window.debugSidebar = {
      collapse: () => toggleSidebar(true),
      expand: () => toggleSidebar(false),
      getState: () => sidebar.classList.contains('w-16') ? 'collapsed' : 'expanded',
      getSavedState: () => localStorage.getItem('sidebar-collapsed')
    };
  }
}
