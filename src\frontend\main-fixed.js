"use strict";

import { initSidebar } from './modules/sidebar.js';
import { initMobile } from './modules/mobile.js';

// Initialize sidebar immediately to prevent layout shift
function initializeSidebarState() {
  const sidebar = document.getElementById('sidebar');
  if (!sidebar) return;

  // Check saved state and apply immediately
  const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
  
  // Apply initial state classes without transition
  sidebar.classList.add('no-transition');
  
  if (isCollapsed) {
    sidebar.classList.remove('w-64');
    sidebar.classList.add('w-16');
  } else {
    sidebar.classList.remove('w-16');
    sidebar.classList.add('w-64');
  }
  
  // Mark as initialized and remove no-transition after a frame
  requestAnimationFrame(() => {
    sidebar.classList.add('initialized');
    sidebar.classList.remove('no-transition');
  });
}

// Initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

function initializeApp() {
  // Initialize sidebar state first to prevent flash
  initializeSidebarState();
  
  // Then initialize interactive functionality
  initSidebar();
  initMobile();
  
  // HTMX event listeners
  setupHTMXEventListeners();
  
  // Additional initialization
  setupErrorHandling();
  setupPerformanceOptimizations();
}

function setupHTMXEventListeners() {
  document.body.addEventListener('htmx:beforeRequest', (event) => {
    // Show loading state
    const target = event.target;
    if (target) {
      target.classList.add('htmx-loading');
    }
    
    // Show loading indicator if specified
    const indicator = document.querySelector(event.detail.requestConfig.indicatorSelector);
    if (indicator) {
      indicator.classList.remove('hidden');
    }
  });

  document.body.addEventListener('htmx:afterRequest', (event) => {
    // Hide loading state
    const target = event.target;
    if (target) {
      target.classList.remove('htmx-loading');
    }
    
    // Hide loading indicator
    const indicator = document.querySelector(event.detail.requestConfig.indicatorSelector);
    if (indicator) {
      indicator.classList.add('hidden');
    }
  });

  document.body.addEventListener('htmx:responseError', (event) => {
    console.error('HTMX Request failed:', event.detail);
    
    // Show user-friendly error message
    showErrorToast('Request failed. Please try again.');
  });

  document.body.addEventListener('htmx:timeout', (event) => {
    console.warn('HTMX Request timed out:', event.detail);
    showErrorToast('Request timed out. Please check your connection.');
  });

  // Handle custom events
  document.body.addEventListener('videoSyncStarted', () => {
    showToast('Starting video sync...', 'info');
  });

  document.body.addEventListener('videoSyncCompleted', () => {
    showToast('Video sync completed!', 'success');
  });
}

function setupErrorHandling() {
  // Global error handler for uncaught errors
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    
    if (process.env.NODE_ENV === 'development') {
      showErrorToast(`Error: ${event.error.message}`);
    } else {
      showErrorToast('Something went wrong. Please refresh the page.');
    }
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    if (process.env.NODE_ENV === 'development') {
      showErrorToast(`Promise rejection: ${event.reason}`);
    }
  });
}

function setupPerformanceOptimizations() {
  // Lazy load images when they come into view
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    // Observe all images with data-src
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }

  // Prefetch important resources on hover
  document.addEventListener('mouseover', (event) => {
    const link = event.target.closest('a[href]');
    if (link && link.hostname === location.hostname) {
      prefetchPage(link.href);
    }
  });
}

// Utility functions
function showToast(message, type = 'info') {
  const toast = createToast(message, type);
  document.body.appendChild(toast);
  
  // Animate in
  setTimeout(() => toast.classList.add('opacity-100', 'translate-y-0'), 100);
  
  // Remove after delay
  setTimeout(() => {
    toast.classList.remove('opacity-100', 'translate-y-0');
    toast.classList.add('opacity-0', 'translate-y-2');
    setTimeout(() => toast.remove(), 300);
  }, 3000);
}

function showErrorToast(message) {
  showToast(message, 'error');
}

function createToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 opacity-0 translate-y-2`;
  
  const colors = {
    info: 'bg-blue-900/90 border-blue-700 text-blue-200',
    success: 'bg-green-900/90 border-green-700 text-green-200',
    error: 'bg-red-900/90 border-red-700 text-red-200',
    warning: 'bg-yellow-900/90 border-yellow-700 text-yellow-200'
  };
  
  toast.className += ` ${colors[type] || colors.info} border`;
  toast.textContent = message;
  
  return toast;
}

function prefetchPage(url) {
  // Simple prefetch implementation
  if (!document.querySelector(`link[rel="prefetch"][href="${url}"]`)) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    document.head.appendChild(link);
  }
}

// Export for debugging in development
if (process.env.NODE_ENV === 'development') {
  window.debugApp = {
    showToast,
    showErrorToast,
    reinitialize: initializeApp
  };
}
