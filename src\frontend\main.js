"use strict";

import { initSidebar } from './modules/sidebar.js';
import { initMobile } from './modules/mobile.js';

// Initialize modules when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initSidebar();
  initMobile();
  
  // HTMX event listeners
  document.body.addEventListener('htmx:beforeRequest', (event) => {
    // Show loading state
    const indicator = document.querySelector(event.detail.requestConfig.indicatorSelector);
    if (indicator) {
      indicator.classList.remove('hidden');
    }
  });

  document.body.addEventListener('htmx:afterRequest', (event) => {
    // Hide loading state
    const indicator = document.querySelector(event.detail.requestConfig.indicatorSelector);
    if (indicator) {
      indicator.classList.add('hidden');
    }
  });

  document.body.addEventListener('htmx:responseError', (event) => {
    console.error('HTMX Request failed:', event.detail);
    // Could show error toast here
  });
});