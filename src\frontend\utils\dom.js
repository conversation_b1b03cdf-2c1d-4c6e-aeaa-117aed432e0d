"use strict";

/**
 * DOM utility functions
 */

export const DOM = {
  /**
   * Get element by ID
   * @param {string} id - Element ID
   * @returns {Element|null}
   */
  get(id) {
    return document.getElementById(id);
  },

  /**
   * Get elements by selector
   * @param {string} selector - CSS selector
   * @param {Element} context - Context element (default: document)
   * @returns {NodeList}
   */
  getAll(selector, context = document) {
    return context.querySelectorAll(selector);
  },

  /**
   * Get single element by selector
   * @param {string} selector - CSS selector
   * @param {Element} context - Context element (default: document)
   * @returns {Element|null}
   */
  getOne(selector, context = document) {
    return context.querySelector(selector);
  },

  /**
   * Add event listener to element
   * @param {Element|string} element - Element or selector
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   */
  on(element, event, handler) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    if (el) {
      el.addEventListener(event, handler);
    }
  },

  /**
   * Add class to element
   * @param {Element|string} element - Element or selector
   * @param {string} className - Class name
   */
  addClass(element, className) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    if (el) {
      el.classList.add(className);
    }
  },

  /**
   * Remove class from element
   * @param {Element|string} element - Element or selector
   * @param {string} className - Class name
   */
  removeClass(element, className) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    if (el) {
      el.classList.remove(className);
    }
  },

  /**
   * Toggle class on element
   * @param {Element|string} element - Element or selector
   * @param {string} className - Class name
   */
  toggleClass(element, className) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    if (el) {
      el.classList.toggle(className);
    }
  },

  /**
   * Check if element has class
   * @param {Element|string} element - Element or selector
   * @param {string} className - Class name
   * @returns {boolean}
   */
  hasClass(element, className) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    return el ? el.classList.contains(className) : false;
  },

  /**
   * Show element
   * @param {Element|string} element - Element or selector
   */
  show(element) {
    this.removeClass(element, 'hidden');
  },

  /**
   * Hide element
   * @param {Element|string} element - Element or selector
   */
  hide(element) {
    this.addClass(element, 'hidden');
  },

  /**
   * Set attribute
   * @param {Element|string} element - Element or selector
   * @param {string} attr - Attribute name
   * @param {string} value - Attribute value
   */
  setAttr(element, attr, value) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    if (el) {
      el.setAttribute(attr, value);
    }
  },

  /**
   * Get attribute
   * @param {Element|string} element - Element or selector
   * @param {string} attr - Attribute name
   * @returns {string|null}
   */
  getAttr(element, attr) {
    const el = typeof element === 'string' ? this.getOne(element) : element;
    return el ? el.getAttribute(attr) : null;
  },

  /**
   * Debounce function
   * @param {Function} func - Function to debounce
   * @param {number} delay - Delay in milliseconds
   * @returns {Function}
   */
  debounce(func, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },

  /**
   * Throttle function
   * @param {Function} func - Function to throttle
   * @param {number} delay - Delay in milliseconds
   * @returns {Function}
   */
  throttle(func, delay) {
    let lastCall = 0;
    return function (...args) {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }
};