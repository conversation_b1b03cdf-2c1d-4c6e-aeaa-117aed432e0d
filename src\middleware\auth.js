"use strict";

/**
 * Authentication middleware and route guards
 */

/**
 * Middleware to require authentication for routes
 * Redirects to sign-in page if not authenticated
 */
export function requireAuth(fastify) {
  return async function(request, reply) {
    try {
      const token = request.cookies.token;
      
      if (!token) {
        return redirectToSignIn(request, reply);
      }
      
      const decoded = fastify.jwt.verify(token);
      request.user = {
        id: decoded.userId,
        email: decoded.email
      };
      
    } catch (err) {
      return redirectToSignIn(request, reply);
    }
  };
}

/**
 * Middleware for optional authentication
 * Sets user if authenticated but doesn't redirect if not
 */
export function optionalAuth(fastify) {
  return async function(request, reply) {
    try {
      const token = request.cookies.token;
      
      if (token) {
        const decoded = fastify.jwt.verify(token);
        request.user = {
          id: decoded.userId,
          email: decoded.email
        };
      }
    } catch (err) {
      // Silently fail for optional auth
      request.user = null;
    }
  };
}

/**
 * Middleware to redirect authenticated users away from auth pages
 */
export function redirectIfAuthenticated(fastify) {
  return async function(request, reply) {
    try {
      const token = request.cookies.token;
      
      if (token) {
        fastify.jwt.verify(token);
        // User is authenticated, redirect to main app
        return reply.redirect('/');
      }
    } catch (err) {
      // Token invalid, continue to auth page
    }
  };
}

/**
 * Helper function to handle redirects for different request types
 */
function redirectToSignIn(request, reply) {
  // For HTMX requests, send redirect header
  if (request.headers['hx-request']) {
    reply.header('HX-Redirect', '/auth/signin');
    return reply.code(401).send({ 
      error: 'Authentication required',
      redirect: '/auth/signin'
    });
  }
  
  // For API requests, send JSON response
  if (request.url.startsWith('/api/')) {
    return reply.code(401).send({ 
      error: 'Authentication required',
      redirect: '/auth/signin'
    });
  }
  
  // For regular page requests, redirect
  return reply.redirect('/auth/signin');
}

/**
 * Check if user has required permissions
 */
export function requirePermission(permission) {
  return async function(request, reply) {
    if (!request.user) {
      return reply.code(401).send({ error: 'Authentication required' });
    }
    
    // Add permission checking logic here
    // For now, all authenticated users have all permissions
    
    // Example: Check user role/permissions from database
    // const hasPermission = await checkUserPermission(request.user.id, permission);
    // if (!hasPermission) {
    //   return reply.code(403).send({ error: 'Insufficient permissions' });
    // }
  };
}

/**
 * Rate limiting for authentication endpoints
 */
export function authRateLimit() {
  return {
    max: 5, // 5 attempts
    timeWindow: '15 minutes',
    errorResponseBuilder: function(request, context) {
      return {
        code: 429,
        error: 'Too many authentication attempts',
        message: `Rate limit exceeded, retry in ${Math.round(context.ttl / 1000)} seconds.`
      };
    }
  };
}

/**
 * Validate session and refresh if needed
 */
export async function validateSession(fastify, request, reply) {
  try {
    const token = request.cookies.token;
    
    if (!token) {
      return null;
    }
    
    const decoded = fastify.jwt.verify(token);
    
    // Check if token is close to expiration (within 1 hour)
    const tokenExp = decoded.exp * 1000; // Convert to milliseconds
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    if (tokenExp - now < oneHour) {
      // Token expires soon, refresh it
      const newToken = fastify.jwt.sign({
        userId: decoded.userId,
        email: decoded.email
      });
      
      reply.setCookie('token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });
    }
    
    return {
      id: decoded.userId,
      email: decoded.email
    };
    
  } catch (err) {
    return null;
  }
}

/**
 * Session cleanup helper
 */
export function clearSession(reply) {
  reply.clearCookie('token');
  reply.clearCookie('refresh_token');
}

/**
 * Security headers middleware
 */
export function securityHeaders() {
  return async function(request, reply) {
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    if (process.env.NODE_ENV === 'production') {
      reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }
  };
}

/**
 * CSRF protection for forms
 */
export function csrfProtection() {
  return async function(request, reply) {
    // Skip CSRF for GET requests and API calls
    if (request.method === 'GET' || request.url.startsWith('/api/')) {
      return;
    }
    
    const token = request.body?.csrf_token || request.headers['x-csrf-token'];
    const sessionToken = request.cookies.csrf_token;
    
    if (!token || !sessionToken || token !== sessionToken) {
      return reply.code(403).send({ error: 'Invalid CSRF token' });
    }
  };
}

/**
 * Generate CSRF token
 */
export function generateCSRFToken() {
  return require('crypto').randomBytes(32).toString('hex');
}
