<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= title %> - VirSnapp</title>
  <link href="/public/css/style.css" rel="stylesheet">
  <script src="https://unpkg.com/htmx.org@1.9.10"></script>
  <script src="/public/js/app.min.js" defer></script>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen flex items-center justify-center">
  <div class="w-full max-w-md">
    <!-- Logo Section -->
    <div class="text-center mb-8">
      <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-white">VirSnapp</h1>
      <p class="text-gray-400 mt-2">YouTube Channel Tracker</p>
    </div>

    <!-- Main Content -->
    <div class="bg-gray-800 rounded-xl shadow-2xl p-8 border border-gray-700">
      <%- body %>
    </div>

    <!-- Footer -->
    <div class="text-center mt-8 text-gray-500 text-sm">
      <p>&copy; 2024 VirSnapp. All rights reserved.</p>
    </div>
  </div>

  <!-- Toast Container -->
  <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

  <!-- Loading Overlay -->
  <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      <span class="text-white">Loading...</span>
    </div>
  </div>
</body>
</html>
