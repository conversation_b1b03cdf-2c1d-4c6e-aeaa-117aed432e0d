<aside 
  id="sidebar" 
  class="w-64 bg-gray-800 border-r border-gray-700 transition-all duration-300 ease-in-out flex flex-col fixed lg:static inset-y-0 left-0 z-50 lg:z-auto transform -translate-x-full lg:translate-x-0"
>
  <!-- Logo -->
  <div class="flex items-center justify-between p-4 border-b border-gray-700">
    <div class="flex items-center space-x-3">
      <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      </div>
      <span class="text-xl font-bold text-white sidebar-text leading-none">VirSnapp</span>
    </div>
    <button 
      id="sidebar-collapse-button" 
      class="w-8 h-8 rounded-lg text-gray-400 hover:bg-gray-700 transition-colors hidden lg:flex items-center justify-center flex-shrink-0"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
      </svg>
    </button>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 p-3 space-y-1">
    <a 
      href="/tracked-channels" 
      class="nav-item group flex items-center w-full h-12 px-3 rounded-lg transition-all duration-200 <%= currentPage === 'tracked-channels' ? 'bg-green-900/20 text-green-300' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>"
    >
      <div class="nav-icon-container flex items-center justify-center w-5 h-5 flex-shrink-0">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
          <!-- GPS/Location tracking icon with target crosshairs -->
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
          <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25s-7.5-4.108-7.5-11.25a7.5 7.5 0 1 1 15 0Z" />
        </svg>
      </div>
      <span class="sidebar-text ml-3 font-medium leading-none select-none">Tracked channels</span>
    </a>

    <a 
      href="/saved-videos" 
      class="nav-item group flex items-center w-full h-12 px-3 rounded-lg transition-all duration-200 <%= currentPage === 'saved-videos' ? 'bg-green-900/20 text-green-300' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>"
    >
      <div class="nav-icon-container flex items-center justify-center w-5 h-5 flex-shrink-0">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
        </svg>
      </div>
      <span class="sidebar-text ml-3 font-medium leading-none select-none">Saved videos</span>
    </a>

    <a 
      href="/settings" 
      class="nav-item group flex items-center w-full h-12 px-3 rounded-lg transition-all duration-200 <%= currentPage === 'settings' ? 'bg-green-900/20 text-green-300' : 'text-gray-300 hover:bg-gray-700 hover:text-white' %>"
    >
      <div class="nav-icon-container flex items-center justify-center w-5 h-5 flex-shrink-0">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
        </svg>
      </div>
      <span class="sidebar-text ml-3 font-medium leading-none select-none">Settings</span>
    </a>
  </nav>
</aside>