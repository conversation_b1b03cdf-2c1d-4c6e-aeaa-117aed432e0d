"use strict";

export function initMobile() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const sidebar = document.getElementById('sidebar');
  const overlay = document.getElementById('mobile-overlay');
  
  if (!mobileMenuButton || !sidebar || !overlay) return;

  mobileMenuButton.addEventListener('click', () => {
    toggleMobileSidebar();
  });

  overlay.addEventListener('click', () => {
    closeMobileSidebar();
  });

  // Close sidebar when clicking nav links on mobile
  const navItems = sidebar.querySelectorAll('.nav-item');
  navItems.forEach(item => {
    item.addEventListener('click', () => {
      if (window.innerWidth < 1024) {
        closeMobileSidebar();
      }
    });
  });

  function toggleMobileSidebar() {
    const isOpen = !sidebar.classList.contains('-translate-x-full');
    
    if (isOpen) {
      closeMobileSidebar();
    } else {
      openMobileSidebar();
    }
  }

  function openMobileSidebar() {
    sidebar.classList.remove('-translate-x-full');
    sidebar.classList.add('animate-slide-in');
    overlay.classList.remove('hidden');
    overlay.classList.add('animate-fade-in');
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  function closeMobileSidebar() {
    sidebar.classList.add('-translate-x-full');
    sidebar.classList.remove('animate-slide-in');
    sidebar.classList.add('animate-slide-out');
    overlay.classList.add('hidden');
    overlay.classList.remove('animate-fade-in');
    overlay.classList.add('animate-fade-out');
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Clean up animation classes
    setTimeout(() => {
      sidebar.classList.remove('animate-slide-out');
      overlay.classList.remove('animate-fade-out');
    }, 300);
  }
}