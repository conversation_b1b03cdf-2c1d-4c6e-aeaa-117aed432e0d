"use strict";

/**
 * Performance monitoring utilities for database queries and API endpoints
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.slowQueryThreshold = 100; // 100ms threshold for slow queries
    this.logSlowQueries = true;
  }

  /**
   * Start timing an operation
   * @param {string} operationName - Name of the operation
   * @returns {Object} Timer object with end() method
   */
  startTimer(operationName) {
    const startTime = process.hrtime.bigint();
    
    return {
      end: () => {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        
        this.recordMetric(operationName, duration);
        
        if (duration > this.slowQueryThreshold && this.logSlowQueries) {
          console.warn(`🐌 Slow operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
        }
        
        return duration;
      }
    };
  }

  /**
   * Record a performance metric
   * @param {string} operationName - Name of the operation
   * @param {number} duration - Duration in milliseconds
   */
  recordMetric(operationName, duration) {
    if (!this.metrics.has(operationName)) {
      this.metrics.set(operationName, {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        avgTime: 0
      });
    }
    
    const metric = this.metrics.get(operationName);
    metric.count++;
    metric.totalTime += duration;
    metric.minTime = Math.min(metric.minTime, duration);
    metric.maxTime = Math.max(metric.maxTime, duration);
    metric.avgTime = metric.totalTime / metric.count;
  }

  /**
   * Get performance statistics for an operation
   * @param {string} operationName - Name of the operation
   * @returns {Object|null} Performance stats or null if not found
   */
  getStats(operationName) {
    return this.metrics.get(operationName) || null;
  }

  /**
   * Get all performance statistics
   * @returns {Object} All performance stats
   */
  getAllStats() {
    const stats = {};
    for (const [name, metric] of this.metrics.entries()) {
      stats[name] = {
        ...metric,
        avgTime: Math.round(metric.avgTime * 100) / 100,
        minTime: Math.round(metric.minTime * 100) / 100,
        maxTime: Math.round(metric.maxTime * 100) / 100
      };
    }
    return stats;
  }

  /**
   * Get slow operations (above threshold)
   * @returns {Array} Array of slow operations
   */
  getSlowOperations() {
    const slowOps = [];
    for (const [name, metric] of this.metrics.entries()) {
      if (metric.avgTime > this.slowQueryThreshold) {
        slowOps.push({
          name,
          avgTime: Math.round(metric.avgTime * 100) / 100,
          maxTime: Math.round(metric.maxTime * 100) / 100,
          count: metric.count
        });
      }
    }
    return slowOps.sort((a, b) => b.avgTime - a.avgTime);
  }

  /**
   * Reset all metrics
   */
  reset() {
    this.metrics.clear();
  }

  /**
   * Log performance summary
   */
  logSummary() {
    const stats = this.getAllStats();
    const slowOps = this.getSlowOperations();
    
    console.log('\n📊 Performance Summary:');
    console.log('========================');
    
    if (Object.keys(stats).length === 0) {
      console.log('No performance data collected yet.');
      return;
    }
    
    // Log top operations by frequency
    const topOps = Object.entries(stats)
      .sort((a, b) => b[1].count - a[1].count)
      .slice(0, 5);
    
    console.log('\n🔥 Most Frequent Operations:');
    topOps.forEach(([name, metric]) => {
      console.log(`  ${name}: ${metric.count} calls, avg ${metric.avgTime}ms`);
    });
    
    // Log slow operations
    if (slowOps.length > 0) {
      console.log('\n🐌 Slow Operations (>' + this.slowQueryThreshold + 'ms):');
      slowOps.forEach(op => {
        console.log(`  ${op.name}: avg ${op.avgTime}ms, max ${op.maxTime}ms (${op.count} calls)`);
      });
    } else {
      console.log('\n✅ No slow operations detected!');
    }
    
    console.log('========================\n');
  }

  /**
   * Middleware for Fastify to automatically track route performance
   * @param {Object} fastify - Fastify instance
   */
  createFastifyMiddleware(fastify) {
    fastify.addHook('onRequest', async (request, reply) => {
      request.perfTimer = this.startTimer(`${request.method} ${request.url}`);
    });
    
    fastify.addHook('onResponse', async (request, reply) => {
      if (request.perfTimer) {
        const duration = request.perfTimer.end();
        
        // Log slow requests
        if (duration > this.slowQueryThreshold) {
          fastify.log.warn(`Slow request: ${request.method} ${request.url} - ${duration.toFixed(2)}ms`);
        }
      }
    });
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Log summary every 5 minutes in development
if (process.env.NODE_ENV !== 'production') {
  setInterval(() => {
    performanceMonitor.logSummary();
  }, 5 * 60 * 1000);
}

export { performanceMonitor };
