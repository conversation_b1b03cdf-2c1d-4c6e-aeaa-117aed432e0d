"use strict";

import { requireAuth, optionalAuth } from '../middleware/auth.js';

async function routes(fastify, options) {
  // Main dashboard route - requires authentication
  fastify.get('/', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    return reply.view('pages/tracked-channels', {
      title: 'Tracked Channels',
      currentPage: 'tracked-channels',
      user: request.user
    });
  });

  // Navigation routes - all require authentication
  fastify.get('/tracked-channels', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    return reply.view('pages/tracked-channels', {
      title: 'Tracked Channels',
      currentPage: 'tracked-channels',
      user: request.user
    });
  });

  fastify.get('/saved-videos', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    return reply.view('pages/saved-videos', {
      title: 'Saved Videos',
      currentPage: 'saved-videos',
      user: request.user
    });
  });

  fastify.get('/settings', {
    preHandler: [requireAuth(fastify)]
  }, async (request, reply) => {
    return reply.view('pages/settings', {
      title: 'Settings',
      currentPage: 'settings',
      user: request.user
    });
  });
}

export default routes;