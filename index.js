import Fastify from 'fastify';
import fastifyStatic from '@fastify/static';
import fastifyView from '@fastify/view';
import fastifySchedule from '@fastify/schedule';
import fastifyJwt from '@fastify/jwt';
import fastifyCookie from '@fastify/cookie';
import ejs from 'ejs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Import routes
import indexRoutes from './src/routes/index.js';
import videoRoutes from './src/routes/videos.js';
import authRoutes from './src/routes/auth-enhanced.js';

// Import middleware
import { requireAuth, optionalAuth, redirectIfAuthenticated, securityHeaders } from './src/middleware/auth.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const fastify = Fastify({
  logger: process.env.NODE_ENV === 'development'
});

// Register plugins
await fastify.register(fastifyStatic, {
  root: join(__dirname, 'public'),
  prefix: '/public/'
});

await fastify.register(fastifyView, {
  engine: {
    ejs: ejs
  },
  root: join(__dirname, 'src/views'),
  layout: 'layouts/main'
});

await fastify.register(fastifyJwt, {
  secret: process.env.JWT_SECRET || 'your-secret-key'
});

await fastify.register(fastifyCookie);

await fastify.register(fastifySchedule);

// Register form body parser for HTMX form submissions
await fastify.register(import('@fastify/formbody'));

// Register multipart for file uploads
await fastify.register(import('@fastify/multipart'));

// Add security headers middleware
fastify.addHook('onRequest', securityHeaders());

// Register routes
await fastify.register(authRoutes);
await fastify.register(indexRoutes);
await fastify.register(videoRoutes);

// Start server
const start = async () => {
  try {
    const port = process.env.FASTIFY_PORT || 3000;
    await fastify.listen({ port, host: '0.0.0.0' });
    console.log(`Server running on http://localhost:${port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();