@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans antialiased bg-gray-900 text-gray-100;
  }
}

@layer components {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Standardized page titles */
  .page-title {
    font-size: 2.25rem; /* text-4xl */
    font-weight: 700; /* font-bold */
    color: white;
    margin-bottom: 2rem; /* mb-8 */
    line-height: 1.1;
    letter-spacing: -0.025em;
  }
  
  /* Page container standardization */
  .page-container {
    max-width: 80rem; /* max-w-6xl - consistent across all pages */
    margin: 0 auto;
    padding: 0 1rem; /* px-4 for mobile spacing */
  }
  
  /* Mobile title adjustments */
  @media (max-width: 1023px) {
    .page-title {
      display: none; /* Hide on mobile to avoid duplication with mobile header */
    }
    
    .page-container {
      padding: 0 0.5rem; /* Reduced mobile padding */
    }
  }
  
  /* Desktop title adjustments */
  @media (min-width: 1024px) {
    .page-title {
      display: block;
      margin-top: 0;
      margin-bottom: 2rem;
    }
    
    .page-container {
      padding: 0 1.5rem; /* Increased desktop padding */
    }
  }
  
  /* Sidebar navigation improvements */
  .nav-item {
    position: relative;
    min-height: 44px; /* Consistent touch target */
    display: flex;
    align-items: center;
    transition: all 0.3s ease-in-out;
  }
  
  .nav-item svg {
    flex-shrink: 0;
    transition: all 0.2s ease-in-out;
  }
  
  .nav-item .sidebar-text {
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
    white-space: nowrap;
  }
  
  /* Icon container for perfect alignment */
  .nav-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.2s ease-in-out;
  }
  
  /* Sidebar transition improvements - prevent layout shift */
  #sidebar {
    transition: width 0.3s ease-in-out;
  }
  
  /* Improved collapsed sidebar styles */
  #sidebar.w-16 .nav-item {
    justify-content: center;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  #sidebar.w-16 .nav-item:hover {
    background-color: rgb(55 65 81); /* bg-gray-700 */
  }
  
  /* Logo icon consistency - make nav icons match logo size when collapsed */
  #sidebar.w-16 .nav-icon-container {
    width: 2rem; /* w-8 */
    height: 2rem; /* h-8 */
  }
  
  /* Ensure proper spacing in collapsed nav */
  #sidebar.w-16 nav {
    padding: 0.75rem 0.25rem;
  }
  
  /* Logo centering in collapsed sidebar */
  #sidebar.w-16 > div:first-child {
    justify-content: center !important;
    padding: 1rem;
    position: relative;
  }
  
  /* Collapse button positioning in collapsed state */
  #sidebar.w-16 #sidebar-collapse-button {
    position: absolute;
    top: 50%;
    right: -1.25rem;
    transform: translateY(-50%);
    background-color: rgb(31 41 55); /* bg-gray-800 */
    border: 1px solid rgb(55 65 81); /* border-gray-700 */
    z-index: 20;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  /* Prevent flash of content during initial load */
  #sidebar:not(.initialized) {
    opacity: 0;
  }
  
  #sidebar.initialized {
    opacity: 1;
    transition: opacity 0.2s ease-in-out, width 0.3s ease-in-out;
  }
  
  /* Main content area adjustments */
  #main-content {
    transition: margin-left 0.3s ease-in-out;
  }
  
  /* Responsive adjustments for sidebar states */
  @media (min-width: 1024px) {
    .w-16 ~ main .page-title,
    .w-64 ~ main .page-title {
      transition: all 0.3s ease-in-out;
    }
    
    main .page-container {
      transition: all 0.3s ease-in-out;
    }
  }
}

@layer utilities {
  .animation-delay-75 {
    animation-delay: 75ms;
  }
  
  .animation-delay-150 {
    animation-delay: 150ms;
  }
  
  .animation-delay-300 {
    animation-delay: 300ms;
  }
  
  /* Utility for preventing layout shift */
  .no-transition {
    transition: none !important;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* HTMX loading styles */
.htmx-indicator {
  display: none;
}

.htmx-request .htmx-indicator {
  display: inline;
}

.htmx-request.htmx-indicator {
  display: inline;
}

/* Prevent flicker on page load */
@media (min-width: 1024px) {
  /* Preload state classes to prevent flash */
  .sidebar-preload-collapsed {
    width: 4rem; /* w-16 */
  }
  
  .sidebar-preload-expanded {
    width: 16rem; /* w-64 */
  }
}
