{"name": "velio-app", "version": "1.0.0", "description": "YouTube channel tracker with HTMX and Tailwind", "main": "index.js", "type": "module", "scripts": {"dev": "concurrently \"pnpm run build:css:watch\" \"pnpm run dev:server\"", "dev:server": "nodemon index.js", "start": "node index.js", "build": "pnpm run build:css && pnpm run build:js", "build:css": "tailwindcss -i ./src/frontend/main.css -o ./public/css/style.css --minify", "build:css:watch": "tailwindcss -i ./src/frontend/main.css -o ./public/css/style.css --watch", "build:js": "vite build", "scrape": "node src/playwright/scraper.js", "lint": "eslint src/", "test": "node --test", "test:playwright": "playwright test", "db:init": "node scripts/init-db.js"}, "dependencies": {"@fastify/cookie": "^9.3.1", "@fastify/cors": "^9.0.1", "@fastify/formbody": "^7.4.0", "@fastify/jwt": "^8.0.0", "@fastify/rate-limit": "^9.1.0", "@fastify/schedule": "^4.0.0", "@fastify/static": "^7.0.1", "@fastify/view": "^9.0.0", "@supabase/supabase-js": "^2.39.7", "dotenv": "^16.4.1", "ejs": "^3.1.9", "fastify": "^4.26.1", "joi": "^17.12.0", "node-cache": "^5.1.2", "playwright": "^1.41.2"}, "devDependencies": {"@iconify-json/lucide": "^1.1.165", "@iconify-json/mdi": "^1.1.64", "autoprefixer": "^10.4.17", "concurrently": "^8.2.2", "eslint": "^8.56.0", "nodemon": "^3.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "unplugin-icons": "^0.18.5", "vite": "^5.1.3"}}