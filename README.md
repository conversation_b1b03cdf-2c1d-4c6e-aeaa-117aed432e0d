# VirSnapp - YouTube Channel Tracker

A full-stack web application for tracking YouTube channels and managing video content using HTMX, Tailwind CSS, Fastify, and Supabase.

## Features

- 🌙 **Dark theme only** - clean, consistent dark UI throughout
- 📱 Responsive design with collapsible sidebar
- 🔄 Real-time video syncing via YouTube API
- 🕷️ Web scraping with Playwright
- 💾 Data persistence with Supabase
- ⚡ Fast, minimal JavaScript with HTMX

## Tech Stack

- **Frontend**: HTMX + Tailwind CSS + Vanilla JavaScript
- **Backend**: Fastify (Node.js)
- **Database**: Supabase (PostgreSQL)
- **Icons**: Unplugin Icons (Iconify)
- **Scraping**: Playwright
- **API**: YouTube Data API v3

## Quick Start

### Prerequisites

- Node.js 18+ 
- YouTube Data API key
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd virsnapp-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Build CSS and JavaScript:
```bash
npm run build
```

5. Start the development server:
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## Available Scripts

- `npm run dev` - Start development server with CSS watching
- `npm run start` - Start production server
- `npm run build` - Build CSS and JavaScript for production
- `npm run build:css` - Build Tailwind CSS
- `npm run build:css:watch` - Watch and rebuild CSS
- `npm run build:js` - Build JavaScript with Vite
- `npm run scrape` - Run manual scraping script
- `npm run lint` - Run ESLint
- `npm run test` - Run tests
- `npm run test:playwright` - Run Playwright tests

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Server
FASTIFY_PORT=3000
NODE_ENV=development

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# YouTube API
YOUTUBE_API_KEY=your_youtube_api_key

# JWT
JWT_SECRET=your_jwt_secret_key
```

### Supabase Setup

1. Create a new Supabase project
2. Run the database initialization:
```javascript
import { createTables } from './src/utils/supabase.js';
await createTables();
```

### YouTube API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API key)
5. Add the API key to your `.env` file

## Project Structure

```
virsnapp-app/
├── src/
│   ├── routes/           # Fastify route handlers
│   ├── views/            # EJS templates
│   │   ├── layouts/      # Layout templates
│   │   ├── pages/        # Page templates
│   │   └── partials/     # Reusable components
│   ├── frontend/         # Client-side JavaScript modules
│   ├── playwright/       # Web scraping scripts
│   └── utils/           # Utilities (Supabase, YouTube API)
├── public/              # Static assets
└── config files
```

## Usage

### Adding a Tracked Channel

```javascript
// Add channel to database
const { data, error } = await supabase
  .from('channels')
  .insert({
    name: 'Channel Name',
    channel_id: 'UC_channel_id',
    url: 'https://youtube.com/@channel',
    is_tracked: true
  });
```

### Manual Scraping

```bash
# Scrape a specific channel
npm run scrape https://youtube.com/@channel UC_channel_id
```

### HTMX Integration

The application uses HTMX for dynamic content loading:

```html
<!-- Load videos dynamically -->
<div 
  hx-get="/api/videos"
  hx-trigger="load"
  hx-target="#video-list"
  hx-swap="innerHTML"
>
  Loading...
</div>

<!-- Sync videos with loading indicator -->
<button 
  hx-post="/api/sync-scrape"
  hx-target="#video-list"
  hx-indicator="#sync-indicator"
>
  Sync Videos
</button>
```

## API Endpoints

### GET /api/videos
Returns HTML fragment with video list for HTMX.

### POST /api/sync-scrape
Triggers video synchronization from YouTube API and scraping.

### Navigation Routes
- `GET /` - Tracked channels page
- `GET /tracked-channels` - Tracked channels page
- `GET /saved-videos` - Saved videos page
- `GET /settings` - Settings page

## Development

### Frontend Architecture

The frontend uses ES6 modules with strict mode:

```javascript
// src/frontend/modules/sidebar.js
"use strict";

export function initSidebar() {
  // Sidebar logic
}
```

### Responsive Sidebar

Collapsible sidebar with mobile support:

- Desktop: Click to collapse/expand
- Mobile: Slide-in overlay with hamburger button
- State persistence in localStorage

## Deployment

### Build for Production

```bash
npm run build
NODE_ENV=production npm start
```

### Environment Variables for Production

- Set `NODE_ENV=production`
- Use production Supabase credentials
- Configure proper JWT secret
- Set appropriate FASTIFY_PORT

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

---

Built with ❤️ using modern web technologies.